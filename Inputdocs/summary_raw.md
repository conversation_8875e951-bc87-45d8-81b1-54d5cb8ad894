FEM21010 Portfolio Management: Detailed Course SummaryThis summary provides a comprehensive overview of the course, designed to help refresh your understanding of all topics, including those from earlier lectures, in preparation for your resit.Part I: Short-Term and Small-N AllocationThis section laid the groundwork for understanding portfolio optimization in a relatively simpler setting, focusing on a small number of assets (N) and a short investment horizon.Lecture 1: Introduction to Portfolio ManagementCore Idea: This lecture set the stage by introducing fundamental empirical observations in finance.Key Concepts Introduced:[[Content/Key Concepts/Equity Premium|Equity Premium]]: The historical excess return of stocks over risk-free assets. The lecture touched upon the "[[Content/Key Concepts/Equity Premium Puzzle|Equity Premium Puzzle]]" (Me<PERSON> and Prescott, 1985), questioning why this premium is so large.[[Content/Key Concepts/Diversification|Diversification]]: Emphasized as crucial. Bessembinder (2018) was cited to show that while diversified market indices show a premium, most individual stocks do not outperform T-bills, highlighting the role of skewness and the importance of holding the few big winners.[[Content/Key Concepts/CAPM|CAPM (Capital Asset Pricing Model)]]: Introduced as the classic model linking expected excess return to market beta: E(Ri,t​)−Rf,t​=βim​[E(Rm,t​)−Rf,t​]. Its historical success and subsequent challenges from anomalies were noted.[[Content/Key Concepts/Fama-French Model|Fama-French Model]]: Presented as an extension to CAPM, adding size (SMB) and value (HML) factors to better explain cross-sectional returns: E(Ri,t​)−Rf,t​=βim​[E(Rm,t​)−Rf,t​]+βis​E(SMBt​)+βih​E(HMLt​).[[Content/Key Concepts/Factor Zoo|Factor Zoo]]: The proliferation of factors beyond Fama-French (e.g., [[Content/Key Concepts/Momentum|Momentum]], [[Content/Key Concepts/Reversal|Reversal]]) was introduced, along with concerns about data dredging (Fama and French, 2018).[[Content/Key Concepts/Return Predictability|Return Predictability]]: The idea that some variables (e.g., dividend/price ratio) can forecast future returns, especially over longer horizons, challenging the random walk hypothesis.Shift in Views: Contrasted traditional finance views (CAPM, unpredictable returns) with modern views (multifactor models, predictable returns).Main Takeaway: Portfolio theory operates in a world more complex than initially thought, with multiple risk dimensions and predictable elements.Lecture 2: Markowitz Portfolio TheoryCore Idea: Introduced the foundational [[Content/Key Concepts/Modern Portfolio Theory|Markowitz framework]] for portfolio selection based on mean-variance optimization.Key Concepts & Derivations:[[Content/Key Concepts/Efficient Frontier|Efficient Frontier]]: The set of portfolios offering the highest expected return for a given level of risk (variance) or the lowest risk for a given expected return.Without a risk-free asset: The frontier is a hyperbola. Optimal weights wMV​=Λ0​+Λ1​μp​, where Λ0​=D1​[BΣ−1ι−AΣ−1μ] and Λ1​=D1​[CΣ−1μ−AΣ−1ι] (with A=ι′Σ−1μ, B=μ′Σ−1μ, C=ι′Σ−1ι, D=BC−A2).[[Content/Key Concepts/Global Minimum Variance Portfolio|Global Minimum Variance Portfolio (GMV)]]: The portfolio on the efficient frontier with the lowest possible variance. Its weights depend only on the covariance matrix: wGMV​=ι′Σ−1ιΣ−1ι​. This portfolio is often more stable empirically because it doesn't rely on expected return estimates.With a risk-free asset: The efficient frontier becomes the [[Content/Key Concepts/Capital Allocation Line|Capital Allocation Line (CAL)]], a straight line from the risk-free asset through the [[Content/Key Concepts/Tangency Portfolio|Tangency Portfolio]].[[Content/Key Concepts/Tangency Portfolio|Tangency Portfolio (TP)]]: The portfolio of risky assets with the highest [[Content/Key Concepts/Sharpe Ratio|Sharpe Ratio]]. All investors, regardless of risk aversion, would combine the risk-free asset with this TP. Weights: wTAN​=ι′Σ−1μ~​Σ−1μ~​​, where μ~​ is the vector of expected excess returns.[[Content/Key Concepts/Certainty Equivalent|Certainty Equivalent (CE)]]: Mean-variance optimization is equivalent to maximizing CE (e.g., CE≈μp​−21​γσp2​) under quadratic utility or normal returns.Factor Models in Markowitz: [[Content/Key Concepts/Factor Models for Portfolio Optimization|Factor models]] (CAPM, Fama-French) provide structured ways to estimate inputs μ (expected returns) and Σ (covariance matrix).If CAPM holds, TP is the market portfolio.If a multifactor model holds and alphas are zero, optimal investment is in factors and the risk-free asset.The factor structure for Σ is Σ=BΣf​B′+Σϵ​. The inverse can be found using the Woodbury identity: Σ−1=Σϵ−1​−Σϵ−1​B(B′Σϵ−1​B+Σf−1​)−1B′Σϵ−1​.Main Takeaway: Markowitz provides a quantitative framework for diversification, but its practical success hinges on the quality of input estimates.Lecture 3: Regression Perspective and Testing Portfolio PerformanceCore Idea: To reframe portfolio optimization problems and performance tests using regression analysis, offering alternative estimation and inference methods.Key Concepts & Techniques:[[Content/Key Concepts/Inverse Covariance Matrix|Inverse Covariance Matrix (Σ−1)]] from Regressions (Stevens, 1998):Elements of Σ−1 can be derived from coefficients (ϕi,j​) and residual variances (V(ϵi​)) of regressions of each asset ri​ on all other assets rj​: θi,i​=V(ϵi​)−1 and θi,j​=−ϕi,j​V(ϵi​)−1.This provides an interpretation of Σ−1 in terms of optimal hedging portfolios.[[Content/Key Concepts/Britten-Jones Regression|Britten-Jones (1999) Regression]] for Tangency Portfolio:Regressing a vector of ones on excess returns (1=bRt+1​+ut+1​) yields OLS estimate b^ proportional to tangency portfolio weights: wTAN​=ι′b^b^​. This allows standard OLS inference (t-tests, F-tests) on portfolio weights.[[Content/Key Concepts/Kempf-Memmel Regression|Kempf-Memmel (2006) Regression]] for GMV Portfolio:Regressing rt1​ on (rt1​−rtj​) for j=2,...,N yields GMV weights.[[Content/Key Concepts/Sharpe Ratio Testing|Testing Portfolio Performance (Sharpe Ratios)]] (Ledoit & Wolf, 2008):To test H0​:SRi​=SRj​, one tests if Δ=SRi​−SRj​=0.The test statistic T​(Δ^−Δ) is asymptotically normal.Standard errors for Δ^ require robust methods like [[Content/Key Concepts/HAC Inference|HAC (Heteroskedasticity and Autocorrelation Consistent) estimators]] (e.g., Newey-West) or, preferably in small samples, [[Content/Key Concepts/Bootstrap Methods|time-series bootstrap methods]] (studentized bootstrap).Testing Variances (Ledoit & Wolf, 2011): Similar robust methods are used to test differences in log variances.Main Takeaway: Regression techniques offer alternative ways to estimate and test portfolio allocations, and robust statistical methods are crucial for reliable performance comparisons.Lecture 4: Implementing Markowitz Portfolios & Empirical ProblemsCore Idea: Discussed the practical challenges of implementing Markowitz optimization, focusing on different estimation philosophies and the severe impact of estimation error.Implementation Approaches:[[Content/Key Concepts/Plug-in Approach|Plug-in Approach]]: Estimate parameters (θ∗=(μ^​,Σ^)) and use them as if true.Frequentist: Use sample moments. Uncertainty via sampling distribution of weights (Delta method for standard errors).Bayesian: Use posterior moments (e.g., E(μ∣Y),E(Σ∣Y)). Uncertainty via posterior distribution of weights.[[Content/Key Concepts/Decision-Theoretic Approach|Decision-Theoretic Approach]]: Accounts for parameter uncertainty by optimizing expected utility with respect to the predictive distribution of returns: p(Rt+1​∣YT​)=∫p(Rt+1​∣θ)p(θ∣YT​)dθ. This is generally more robust. For short horizons, differences from Bayesian plug-in might be small.[[Content/Key Concepts/Jobson-Korkie Experiment|Jobson-Korkie Experiment (1980)]]:A seminal simulation study showing that sample-based mean-variance efficient frontiers are highly unstable and perform poorly out-of-sample compared to the true frontier. Increasing sample size helps but doesn't fully resolve the issue.[[Content/Key Concepts/Error Maximization|Error Maximization (Michaud, 1989)]]:Mean-variance optimizers tend to overweight assets with positive estimation errors in their expected returns and/or negative errors in their variances/covariances. The optimizer essentially "optimizes the noise."Main Takeaway: Naive application of Markowitz with sample estimates is problematic due to "error maximization." More sophisticated approaches are needed.Lecture 5: Estimation Risk and Shrinkage EstimationCore Idea: Quantifying estimation risk and introducing shrinkage as a primary tool to combat it.[[Content/Key Concepts/Kan-Zhao Framework|Kan and Zhao (2007) Framework]]:Provides analytical formulas for the expected utility loss due to parameter uncertainty, assuming i.i.d. normal excess returns.Analyzes three cases: uncertain μ (loss ∝N/T), uncertain Σ (loss ∝SRtan2​×f(N,T)), and both uncertain.Crucially, shows that for large N/T, uncertainty in Σ can be as damaging as uncertainty in μ.Suggests optimal adjustments: scaling the plug-in portfolio or forming combinations (e.g., tangency + GMV). The optimal scaling factor c∗∗ for woptimal​=c∗∗wplugin​ depends on the true Sharpe ratio and N/T.[[Content/Key Concepts/Shrinkage Estimation|Shrinkage Estimation]]:Improves estimates by pulling sample estimates towards a more structured, often more stable, target. This introduces bias but can significantly reduce variance, leading to lower Mean Squared Error (MSE).[[Content/Key Concepts/Bias-Variance Tradeoff|Bias-Variance Tradeoff]]: MSE(θ^)=Bias(θ^)2+Var(θ^).[[Content/Key Concepts/James-Stein Estimator|James-Stein Estimator]]: For N≥3 means, it dominates the sample mean in MSE by shrinking individual means towards a common target (e.g., grand mean). Shrinkage intensity δ in μJS∗​=δμ0​+(1−δ)μ^​ depends on N,T, and distance to target.Bayesian Interpretation: Shrinkage is equivalent to using an informative prior; the prior mean is the target, and prior precision dictates shrinkage intensity.Main Takeaway: Parameter uncertainty leads to quantifiable economic losses. Shrinkage estimation offers a powerful way to improve input estimates by exploiting the bias-variance tradeoff.Lecture 6: Portfolio Constraints and the 1/N StrategyCore Idea: Exploring other methods to improve portfolio performance, specifically portfolio constraints and the surprisingly robust 1/N strategy.[[Content/Key Concepts/Factor Models for Portfolio Optimization|Factor Models]] and [[Content/Key Concepts/Shrinkage Estimation|Shrinkage]] Combined:Factor models (e.g., CAPM, Fama-French) can provide structured targets for shrinkage.Pastor (2000) shrinks alphas towards zero based on a prior belief in an asset pricing model.Ledoit and Wolf (2003) shrink the sample covariance matrix Σ^S​ towards a factor-model-implied covariance matrix Σ^∗: Σ^=δΣ^∗+(1−δ)Σ^S​.[[Content/Key Concepts/Portfolio Constraints|Portfolio Constraints]] (e.g., no short-selling wi​≥0, position limits wi​≤w~):Empirically often improve out-of-sample performance, especially for GMV portfolios.[[Content/Key Concepts/Shrinkage Interpretation of Constraints|Shrinkage Interpretation (Jagannathan and Ma, 2003)]]: Imposing constraints is mathematically equivalent to using a modified (shrunk) covariance matrix in an unconstrained problem. For example, a no-short-sale constraint on asset i effectively reduces its estimated covariances with other assets it would have been shorted against.[[Content/Key Concepts/1-N Portfolio Strategy|1/N Portfolio (Equal Weighting)]]:Allocates 1/N of wealth to each asset. Requires no estimation.DeMiguel, Garlappi, and Uppal (2009) (DGU): Found that 1/N is a very tough benchmark to beat out-of-sample for many sophisticated strategies (including shrinkage and Bayesian methods). This is because it completely avoids estimation error.The "loss from estimation error" often outweighs the "gain from optimal diversification" for complex models.Main Takeaway: Constraints act as implicit shrinkage. The 1/N strategy's robustness highlights the severity of estimation error in practice. Minimum variance portfolios, especially when constrained, tend to perform relatively well.Part II: Large-N Allocation and Long-Term InvestmentsThis section deals with more complex scenarios: when the number of assets is large (potentially N>T) and when the investment horizon is long, requiring dynamic considerations.Lecture 7: 1/N, Optimal Constrained, and Timing Portfolios[[Content/Key Concepts/1-N Portfolio Strategy|1/N Portfolio]] Revisited:Theoretical analysis (Kan-Zhao framework) shows the [[Content/Key Concepts/Critical Sample Size for 1-N|critical sample size (T∗)]] needed for optimized portfolios to beat 1/N can be very large: T∗≈N+((N+1)SRtan2​)/(SRtan2​−SRew2​).DGU simulations confirm this: M (estimation window) needs to be large, especially as N increases.[[Content/Key Concepts/Constrained Mean-Variance Allocation|Optimal Constrained (OC) Mean-Variance Allocation]] (Kirby & Ostdiek, 2012):A portfolio on the efficient frontier that targets a specific expected return, often the expected return of the 1/N portfolio.It's a combination of the Tangency Portfolio (TP) and Global Minimum Variance (GMV) portfolio: wOC​=a⋅wTP​+(1−a)⋅wGMV​, where a=(μtarget​−μGMV​)/(μTP​−μGMV​).Aims to reduce sensitivity to extreme expected return estimates that plague the TP.[[Content/Key Concepts/Volatility Timing|Timing Portfolios]] (Kirby & Ostdiek, 2012):Allocate wealth based on asset characteristics, often ignoring covariances (implicitly assuming a diagonal covariance matrix).Volatility Timing (VT): Weights inversely proportional to variance: wi​∝(1/σi2​)η.[[Content/Key Concepts/Risk-Reward Timing|Risk-Reward Timing (RRT)]]: Weights proportional to mean/variance: wi​∝(μi​/σi2​)η.The parameter η controls timing intensity (η=0 gives 1/N).Simpler to implement (no matrix inversion) and can perform well, especially VT, by focusing on more reliably estimated variances.Main Takeaway: 1/N remains a strong benchmark. OC and Timing Portfolios offer practical alternatives that manage estimation risk by simplifying the problem or imposing sensible constraints.Lecture 8: Large-N Portfolio AllocationCore Challenge: When N>T, the sample covariance matrix Σ^ is singular and not invertible. Even if N≤T but N is large, Σ^ is ill-conditioned.Estimating Large-N Covariance Matrices (Σ):[[Content/Key Concepts/Factor Models for Large-N Covariance Matrices|Factor Models]]: Impose structure Σ=BΣf​B′+Σϵ​. Can restrict Σϵ​ (e.g., diagonal) or use shrinkage (Ledoit-Wolf towards factor-implied Σ).[[Content/Key Concepts/Eigenvalue Shrinkage|Eigenvalue Shrinkage (Ledoit & Wolf, 2004)]]: Decompose Σ^=VDV′ and shrink sample eigenvalues di​ towards a target (e.g., grand mean dˉ), d~i​=(1−α)di​+αdˉ, forming Σ~=VD~V′. This ensures positive definiteness and better conditioning.[[Content/Key Concepts/POET|POET (Principal Orthogonal complEment Thresholding) (Fan, Liao, Mincheva, 2013)]]:Assumes an approximate factor model with K unobserved factors.Uses PCA on Σ^ to estimate factor loadings (eigenvectors) and factor variances (eigenvalues).The residual covariance matrix Σ^ϵ​ is then estimated.Sparsity is imposed on Σ^ϵ​ by thresholding its off-diagonal elements.Σ~=B^Σ^fB^′+Σ~ϵ(sparse).Estimating Large-N Inverse Covariance Matrices (Σ−1) Directly:[[Content/Key Concepts/Nodewise Regression|Nodewise Regression (Meinshausen & Bühlmann, 2006; Callot et al., 2021)]]:For each asset i, regress ri​ on all other assets rj​ using Lasso: ri​=a+∑j=i​ϕij​rj​+ϵi​. The L1 penalty promotes sparse ϕij​.Construct Σ^−1 using θ^ii​=1/V^(ϵi​) and θ^ij=−ϕ^​ij/V^(ϵi​).May require symmetrization and eigenvalue cleaning for positive definiteness.Interprets Σ−1 as optimal sparse hedging portfolios.[[Content/Key Concepts/Graphical Lasso|Graphical Lasso (Friedman, Hastie, Tibshirani, 2008)]]:Estimates Θ=Σ−1 by maximizing a penalized log-likelihood: maxΘ​(logdet(Θ)−tr(SΘ)−λ∣∣Θ∣∣1,off​).The L1 penalty on off-diagonal elements promotes sparsity in Θ.Guarantees a positive definite estimate.Non-zero θij​ implies conditional dependence between asset i and j.Reconciling Factor Models and Sparse Inverse: Even with a factor structure, Σ−1 is generally not sparse. However, the inverse of the residual covariance matrix conditional on factors, Δ−1, can be sparse. Methods like Graphical Lasso can be applied to residuals.Main Takeaway: For large N, specialized techniques focusing on shrinkage, factor structures, and sparsity are essential for estimating Σ or Σ−1.Lecture 9: Long-Term Investors & Portfolio Choice in an i.i.d. WorldCore Idea: Shifting to [[Content/Key Concepts/Strategic Asset Allocation|strategic asset allocation]] for long horizons, typically using a utility-based framework.[[Content/Key Concepts/Conditional Moments|Conditional Moments]]: Et​(Rt+1​) and Σt​ become central if investment opportunities are time-varying.[[Content/Key Concepts/Power Utility|Power Utility]] & Lognormal Returns: A common framework for tractability.U(W)=W1−γ/(1−γ). RRA = γ.Log returns rt+1​=log(1+Rt+1​) are assumed normal.Portfolio log return approximation (e.g., Campbell-Viceira): rp,t+1​−rf,t+1​≈αt′​(rt+1​−rf,t+1​ι)+21​αt′​σt2​−21​αt′​Σt​αt​.[[Content/Key Concepts/Long-Term Investment Strategies|Long-Term Investment Strategies]] in an i.i.d. World:Myopic Strategy: Optimizes one period ahead. Optimal allocation αt​=γ1​Σt−1​(Et​(rt+1​−rf,t+1​ι)+21​σt2​).Buy-and-Hold: Invests once, holds till horizon.Constant Proportion: Rebalances to fixed proportions.[[Content/Key Concepts/Dynamic Portfolio Choice|Dynamic Allocation]]: Allows weights to change each period.Key Result: If returns are i.i.d., all these strategies lead to the same constant allocation as the myopic one. The horizon doesn't matter.Fallacies of Long-Term Investing:[[Content/Key Concepts/Time Diversification Fallacy|Time Diversification]]: The mistaken belief that stocks become less risky simply because the horizon is longer. While Sharpe Ratios of cumulative returns increase with K​, this doesn't mean risk (e.g., variance of terminal wealth) decreases or that allocations should change in an i.i.d. world with power utility.[[Content/Key Concepts/Expected Log Return Maximization|Maximizing Expected Log Return (Kelly Criterion)]]: Optimal only for log utility investors (γ=1). Other risk-averse investors will prefer less risky portfolios.Main Takeaway: In a simple i.i.d. world with power utility, long-term allocation is no different from short-term. The complexities of long-term investing arise when returns are predictable.Lecture 10: Dynamic Optimization & Hedging DemandsCore Idea: Analyzing long-term allocation when returns are not i.i.d., meaning investment opportunities (conditional means, variances) change over time, predicted by state variables zt​.[[Content/Key Concepts/Bellman Equation|Bellman Equation]]: The cornerstone of dynamic programming. It recursively defines the value function V(τ,Wt​,zt​) (max expected utility with τ periods left):V(τ,Wt​,zt​)=maxxt​​Et​[V(τ−1,Wt+1​,zt+1​)], with Wt+1​=Wt​(xt′​rt+1​+Rf,t​).For power utility, V(τ,Wt​,zt​)=1−γWt1−γ​​ψ(τ,zt​), simplifying the problem.[[Content/Key Concepts/Hedging Demand|Hedging Demand]]: When returns are predictable and correlated with state variables, the optimal dynamic portfolio xt∗​ differs from the myopic portfolio.xt∗​=Myopic Demand+Hedging Demand.Myopic demand exploits current expected returns.Hedging demand arises from the desire to hedge against unfavorable shifts in future investment opportunities (changes in zt​). It's zero if returns are i.i.d., state variables are unhedgeable, or utility is logarithmic (γ=1).[[Content/Key Concepts/Numerical Methods for Dynamic Optimization|Numerical Solution Methods]]:Backward Recursion: Solve the Bellman equation period by period, from terminal date backward.Simulation: Generate paths of returns and state variables from a model (e.g., VAR).[[Content/Key Concepts/Across-Path Regressions|Across-Path Regressions]]: Approximate conditional expectations Et​[⋅] by regressing future outcomes on current state variables zt​ using simulated paths. This is used within the backward recursion to find optimal xt​ at each step. (Brandt and Van Binsbergen, 2007, detail this).Continuous Time Insights (Merton): Merton's framework also shows the myopic + hedging demand structure. The optimal portfolio is xt∗​=γ1​(Σtp​)−1μtp​+βzr​γη​, where η is aversion to state variable risk and βzr​ captures the covariance of asset returns with state variable innovations.[[Content/Key Concepts/Average Investor Theorem|Average Investor Theorem]]: In equilibrium, the market portfolio is optimal for an investor with average risk aversion (γ(M)) and average state variable aversion (η(M)). An individual investor i's optimal portfolio xt(i)∗​ deviates from the market portfolio xt(M)∗​ based on differences in their γ(i) and η(i) from the market averages: xt(i)​=γ(i)γ(M)​xt(M)​+βzr​γ(i)η(i)−η(M)​.Main Takeaway: Predictable returns create hedging demands, making dynamic strategies optimal. Numerical methods are generally required to solve these problems.Lecture 11: Implementation and Parameter Uncertainty in Long-Term AllocationCore Idea: Examining the impact of parameter uncertainty on long-term strategies and exploring alternative modeling approaches.[[Content/Key Concepts/Parameter Uncertainty in Long-Term Allocation|Parameter Uncertainty in Long-Term Allocation]]:Estimation errors compound more significantly over longer horizons. An error in the mean return has a larger impact on cumulative wealth over many periods.[[Content/Key Concepts/Barberis Framework|Barberis (2000)]]: Using a decision-theoretic approach (Bayesian, accounting for parameter uncertainty) for buy-and-hold strategies:If returns are i.i.d., parameter uncertainty leads to decreasing equity allocation with horizon (stocks appear riskier).If returns are predictable (e.g., by dividend yield), parameter uncertainty still leads to lower equity allocation compared to ignoring it, and reduces sensitivity to the predictive variable. The riskiness from parameter uncertainty can offset or dominate the risk-reduction from mean reversion.[[Content/Key Concepts/Parametric Portfolio Policies|Parameterizing Portfolio Weights Directly]]: An alternative to estimating moments and then optimizing.Large-N (Brandt, Santa-Clara, Valkanov, 2009): Model weights as a function of asset characteristics: wi,t​=w0,i,t​+Nt​1​θ′yi,t​. Estimate θ by maximizing average sample utility: maxθ​T1​∑U(wt​(θ)′rt+1​). This avoids estimating large covariance matrices. Can be combined with regularization (DeMiguel et al., 2020).Conditional/Timing (Brandt & Santa-Clara, 2006 - [[Content/Key Concepts/Augmented Asset Space|Augmented Asset Space]]): Model weights as a function of state variables: wt​=θ′zt​. This transforms the conditional (dynamic) problem into an unconditional (static) problem on an "augmented asset space" with managed portfolios having returns zt​⊗rt+1​. The optimal w~=vec(θ) for these augmented assets can be found using simple unconditional moments.Main Takeaway: Parameter uncertainty is even more critical for long-term decisions. Parametric portfolio policies offer a practical way to handle complex allocation problems by directly optimizing utility over policy parameters.Lecture 12: Course SummaryThis lecture ties together all the concepts:The theoretical optimality of mean-variance and dynamic programming.The pervasive challenge of parameter uncertainty and estimation risk.The econometric toolkit developed to address these challenges: shrinkage, constraints, factor models, large-N techniques, Bayesian methods, parametric policies.The importance of robust empirical evaluation and benchmarks like 1/N.The distinction between short-term (often tactical) and long-term (strategic, dynamic) allocation.This detailed summary should provide a solid foundation for your resit preparation, covering both the earlier and later parts of the course. Remember to focus on the why behind each method and the trade-offs involved. Good luck!