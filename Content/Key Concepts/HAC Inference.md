# HAC Inference

## Definition
HAC (Heteroskedasticity and Autocorrelation Consistent) Inference refers to statistical methods that provide valid inference in the presence of heteroskedasticity (non-constant variance) and autocorrelation (serial correlation) in the data. These methods are particularly important in financial time series analysis where both issues are common.

## Key Points
- Addresses two common issues in financial time series: heteroskedasticity and autocorrelation
- Provides robust standard errors for parameter estimates
- Essential for valid hypothesis testing in financial applications
- Commonly used in portfolio performance evaluation
- Popular implementations include Newey-West and Andrews estimators

## Heteroskedasticity and Autocorrelation in Financial Data
- **Heteroskedasticity**: Volatility clustering in financial returns (periods of high/low volatility)
- **Autocorrelation**: Serial dependence in returns, especially at higher frequencies
- Both issues invalidate the assumptions of classical statistical inference
- Ignoring these issues leads to incorrect standard errors and misleading hypothesis tests

## HAC Estimators
The most common HAC estimator is the Newey-West estimator, which adjusts the variance-covariance matrix to account for heteroskedasticity and autocorrelation:

```
Σ̂_HAC = Γ̂_0 + Σ(j=1 to q) w(j,q) (Γ̂_j + Γ̂_j')
```

Where:
- Γ̂_0 is the sample variance matrix
- Γ̂_j is the j-th sample autocovariance matrix
- w(j,q) is a weighting function that decreases with lag j
- q is the bandwidth parameter (maximum lag considered)

Common weighting functions include:
- Bartlett kernel: w(j,q) = 1 - j/(q+1)
- Parzen kernel
- Quadratic Spectral kernel

## Applications in Portfolio Analysis
- **Sharpe Ratio Testing**: Robust inference on differences in Sharpe ratios
- **Performance Persistence**: Testing whether performance persists over time
- **Factor Model Testing**: Robust tests of asset pricing models
- **Variance Testing**: Comparing the risk of different strategies
- **Mean-Variance Efficiency Tests**: Testing if a portfolio is mean-variance efficient

## Practical Considerations
- **Bandwidth Selection**: Choosing the appropriate maximum lag (q)
- **Small Sample Properties**: HAC estimators are asymptotic and may have poor small sample properties
- **Pre-whitening**: Filtering the data to reduce autocorrelation before applying HAC
- **Alternative Methods**: Bootstrap methods often perform better in small samples

## Related Concepts
- [[Content/Key Concepts/Bootstrap Methods|Bootstrap Methods]]
- [[Content/Key Concepts/Sharpe Ratio Testing|Sharpe Ratio Testing]]
- [[Content/Key Concepts/Performance Evaluation|Performance Evaluation]]
- [[Content/Key Concepts/Time Series Analysis|Time Series Analysis]]
- [[Content/Key Concepts/Statistical Significance|Statistical Significance]]