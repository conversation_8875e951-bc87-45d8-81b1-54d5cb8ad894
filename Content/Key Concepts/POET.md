# POET (Principal Orthogonal complEment Thresholding)

## Definition
POET (Principal Orthogonal complEment Thresholding) is a method for estimating large covariance matrices developed by <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON> (2013). It combines factor structure modeling with sparsity assumptions on the error covariance matrix, making it particularly suitable for high-dimensional settings where the number of assets (N) may exceed the sample size (T).

## Key Points
- Combines factor structure with sparsity assumptions
- Assumes returns follow an approximate factor model with unobserved factors
- Decomposes the covariance matrix into a low-rank component (factors) and a sparse component (idiosyncratic)
- Uses eigenvalue decomposition to extract the factor structure
- Applies thresholding to the residual covariance matrix to enforce sparsity

## Mathematical Framework
POET assumes the following approximate factor model for returns:

```
r_t = Bf_t + ε_t
```

Where:
- r_t is the N×1 vector of asset returns
- B is the N×K matrix of factor loadings
- f_t is the K×1 vector of common factors
- ε_t is the N×1 vector of idiosyncratic errors

The covariance matrix is decomposed as:

```
Σ = BΣ_f B' + Σ_ε
```

Where:
- Σ_f is the K×K factor covariance matrix
- Σ_ε is the N×N idiosyncratic covariance matrix, assumed to be sparse

## Estimation Procedure

### Step 1: Extract Principal Components
- Perform eigenvalue decomposition of the sample covariance matrix
- Use the first K eigenvectors as estimates of the factor loadings
- K can be determined by information criteria or other methods

### Step 2: Compute Residuals
- Estimate the common component using the extracted factors
- Calculate the residuals by subtracting the common component from returns

### Step 3: Threshold the Residual Covariance Matrix
- Compute the sample covariance matrix of the residuals
- Apply thresholding to set small off-diagonal elements to zero
- Common thresholding rules include hard thresholding, soft thresholding, and adaptive thresholding

### Step 4: Combine Components
- Reconstruct the covariance matrix by combining the low-rank factor component and the thresholded residual covariance matrix

## Thresholding Techniques

### Hard Thresholding
```
s_ij = s_ij × I(|s_ij| > τ)
```
Where I(·) is the indicator function and τ is the threshold.

### Soft Thresholding
```
s_ij = sign(s_ij) × max(|s_ij| - τ, 0)
```

### Adaptive Thresholding
```
s_ij = s_ij × I(|s_ij| > τ_ij)
```
Where τ_ij is an entry-specific threshold.

## Theoretical Properties
- Consistency in high dimensions (N can grow exponentially with T)
- Optimal rate of convergence under certain sparsity conditions
- Positive definiteness guaranteed under mild conditions
- Invertibility even when N > T
- Robustness to heavy-tailed distributions

## Applications in Portfolio Optimization
- **Minimum Variance Portfolios**: Stable and well-conditioned estimates
- **Mean-Variance Optimization**: Combined with robust expected return estimates
- **Risk Decomposition**: Separate systematic and idiosyncratic risk components
- **Large Portfolio Construction**: Handles thousands of assets efficiently
- **Stress Testing**: More reliable covariance estimates for scenario analysis

## Related Concepts
- [[Content/Key Concepts/High-Dimensional Portfolio Allocation|High-Dimensional Portfolio Allocation]]
- [[Content/Key Concepts/Factor Models for Large-N Covariance Matrices|Factor Models for Large-N Covariance Matrices]]
- [[Content/Key Concepts/Eigenvalue Shrinkage|Eigenvalue Shrinkage]]
- [[Content/Key Concepts/Nodewise Regression|Nodewise Regression]]
- [[Content/Key Concepts/Graphical Lasso|Graphical Lasso]]