# Global Minimum Variance Portfolio (GMV)

## Definition
The Global Minimum Variance Portfolio (GMV) is the portfolio on the [[Content/Key Concepts/Efficient Frontier|Efficient Frontier]] that has the lowest possible risk (variance or standard deviation). It represents the most conservative efficient portfolio that can be constructed from a given set of assets.

## Key Points
- Located at the leftmost point of the [[Content/Key Concepts/Efficient Frontier|Efficient Frontier]]
- Depends only on the covariance matrix of asset returns, not on expected returns
- Often more stable and reliable in practice than other mean-variance optimized portfolios
- Requires no assumptions about expected returns, only about the covariance structure
- Empirically found to perform well out-of-sample compared to other optimized portfolios

## Mathematical Derivation
The GMV portfolio is derived by solving the following optimization problem:

- **Objective:** Minimize $w'\Sigma w$ (portfolio variance)
- **Constraint:** $w'\iota = 1$ (weights sum to 1)

Where:
- $w$ is the vector of portfolio weights
- $\Sigma$ is the covariance matrix of asset returns
- $\iota$ is a vector of ones

## Formula for GMV Portfolio Weights
The weights of the GMV portfolio are given by:

```
w_GMV = (Σ^(-1) ι) / (ι' Σ^(-1) ι)
```

Where:
- $w_{GMV}$ is the vector of GMV portfolio weights
- $\Sigma^{-1}$ is the inverse of the covariance matrix
- $\iota$ is a vector of ones
- $\iota'$ is the transpose of $\iota$

## Practical Advantages
- **Estimation Error**: Less affected by estimation error since it doesn't require expected return estimates
- **Stability**: Typically more stable over time than other efficient portfolios
- **Simplicity**: Requires estimation of fewer parameters
- **Performance**: Often performs well empirically despite its simplicity

## Limitations
- May not be optimal for investors seeking higher returns
- Still requires accurate estimation of the covariance matrix
- May lead to concentrated positions in low-volatility assets
- Does not account for expected returns, which might be important for some investors

## Extensions
- **Constrained GMV**: Adding constraints like no short-selling
- **Regularized GMV**: Using techniques like shrinkage to improve covariance estimation
- **Risk Parity**: Related approach that equalizes risk contribution across assets

## Related Concepts
- [[Content/Key Concepts/Efficient Frontier|Efficient Frontier]]
- [[Content/Key Concepts/Modern Portfolio Theory|Modern Portfolio Theory]]
- [[Content/Key Concepts/Tangency Portfolio|Tangency Portfolio]]
- [[Content/Key Concepts/Mean-Variance Optimization|Mean-Variance Optimization]]
- [[Content/Key Concepts/Covariance Matrix|Covariance Matrix]]