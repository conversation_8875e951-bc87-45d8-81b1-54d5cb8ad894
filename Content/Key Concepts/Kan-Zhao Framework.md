# Kan-Zhao Framework

## Definition
The Kan-Zhao Framework, developed by <PERSON> and <PERSON><PERSON> (2007), is an analytical framework that quantifies the economic losses due to parameter uncertainty in portfolio optimization. It provides explicit formulas for the expected utility loss when using estimated parameters instead of true parameters in mean-variance portfolio selection.

## Key Points
- Developed by <PERSON> and <PERSON><PERSON> in 2007
- Quantifies economic losses from parameter uncertainty in portfolio optimization
- Analyzes three cases: uncertain mean, uncertain covariance, and both uncertain
- Shows that uncertainty in covariance can be as important as uncertainty in expected returns
- Provides guidance for optimal portfolio choice under parameter uncertainty

## Mathematical Framework
The framework assumes i.i.d. normal excess returns and analyzes the expected utility loss under three scenarios:

### 1. Uncertain Mean (μ), Known Covariance (Σ)
The economic loss is proportional to N/T:

```
Loss = N/(2γT)
```

Where:
- N is the number of assets
- T is the time series length
- γ is the risk aversion coefficient

### 2. Uncertain Covariance (Σ), Known Mean (μ)
The economic loss is proportional to the squared Sharpe ratio and a factor depending on N and T:

```
Loss = (1/2γ) × μ'Σ⁻¹μ × (1-k)
```

Where:
- μ'Σ⁻¹μ is the squared Sharpe ratio of the tangency portfolio
- k is a complex function of T and N that approaches 1 as T increases

### 3. Both Mean (μ) and Covariance (Σ) Uncertain
The total loss is a combination of the losses from cases 1 and 2. For large N/T, uncertainty in Σ can contribute significantly to the loss.

## Optimal Portfolio Adjustments
The framework suggests two main adjustments to standard mean-variance portfolios:

### 1. Scaling Risky Holdings
The optimal portfolio scales the standard plug-in mean-variance portfolio:

```
w_optimal = c** × w_plugin
```

Where:
- w_plugin = γ⁻¹Σ̂⁻¹μ̂ is the plug-in mean-variance portfolio
- c** is the optimal scaling factor that depends on the true Sharpe ratio and N/T

### 2. Portfolio Combinations
The optimal portfolio is a linear combination of the estimated tangency portfolio and the estimated Global Minimum Variance (GMV) portfolio:

```
w_optimal = α × w_TAN + (1-α) × w_GMV
```

Where the optimal weight α depends on the true parameters and N/T.

## Implications
The Kan-Zhao Framework has several important implications:

- **Dimensionality Effect**: Losses increase with the number of assets (N)
- **Sample Size Effect**: Losses decrease with sample size (T)
- **Covariance Importance**: Uncertainty in covariance matrix can be as important as uncertainty in expected returns
- **Portfolio Adjustment**: Standard mean-variance portfolios should be adjusted (scaled down or combined with GMV)
- **Diversification Tradeoff**: Adding assets increases diversification benefits but also estimation risk

## Related Concepts
- [[Content/Key Concepts/Parameter Uncertainty|Parameter Uncertainty]]
- [[Content/Key Concepts/Jobson-Korkie Experiment|Jobson-Korkie Experiment]]
- [[Content/Key Concepts/Error Maximization|Error Maximization]]
- [[Content/Key Concepts/Shrinkage Estimation|Shrinkage Estimation]]
- [[Content/Key Concepts/Global Minimum Variance Portfolio|Global Minimum Variance Portfolio]]