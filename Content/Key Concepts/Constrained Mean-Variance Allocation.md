# Constrained Mean-Variance Allocation

## Definition
Constrained Mean-Variance Allocation refers to portfolio optimization approaches that impose additional constraints on the standard mean-variance framework, particularly targeting a specific expected return level. These constrained portfolios can be expressed as linear combinations of the tangency portfolio and the global minimum variance portfolio.

## Key Points
- Imposes a target expected return constraint on mean-variance optimization
- Results in a portfolio on the efficient frontier
- Can be expressed as a linear combination of the tangency and GMV portfolios
- Often uses the expected return of the 1/N portfolio as the target
- Helps mitigate extreme allocations caused by estimation error

## Mathematical Formulation
The constrained mean-variance portfolio optimization problem can be formulated as:

```
min_w w'Σw
subject to:
  w'μ = μ_target
  w'ι = 1
```

Where:
- w is the vector of portfolio weights
- Σ is the covariance matrix
- μ is the vector of expected returns
- μ_target is the target expected return
- ι is a vector of ones

## Linear Combination Representation
The solution to this constrained optimization can be expressed as a linear combination:

```
w_OC = a × w_TP + (1-a) × w_GMV
```

Where:
- w_OC is the constrained optimal portfolio
- w_TP is the tangency portfolio
- w_GMV is the global minimum variance portfolio
- a is a coefficient determined by the target return:
  ```
  a = (μ_target - μ_GMV) / (μ_TP - μ_GMV)
  ```
- μ_GMV is the expected return of the GMV portfolio
- μ_TP is the expected return of the tangency portfolio

## Setting the Target Return
Common approaches for setting the target return include:
- Using the expected return of the 1/N portfolio
- Setting a specific risk premium target
- Targeting a particular position on the efficient frontier
- Using a benchmark portfolio's expected return

## Advantages
- **Efficiency**: Results in a portfolio on the efficient frontier
- **Flexibility**: Allows targeting specific return levels
- **Stability**: Often more stable than unconstrained optimization
- **Interpretability**: Clear economic interpretation as a combination of well-understood portfolios
- **Risk Control**: Provides better control over the risk-return tradeoff

## Limitations
- **Estimation Risk**: Still subject to estimation error in inputs
- **Target Uncertainty**: The appropriate target return may be unclear
- **Complexity**: More complex than simple strategies like 1/N or timing portfolios
- **Two-Fund Theorem Assumptions**: Relies on assumptions that may not hold in practice
- **Implementation Challenges**: May still result in extreme weights without additional constraints

## Empirical Performance
Research has shown that:
- Constrained mean-variance portfolios often outperform unconstrained ones
- Performance is sensitive to the choice of target return
- Combining with shrinkage estimation can further improve results
- Adding no-short-selling constraints can enhance stability
- Often provides a good balance between the 1/N portfolio and full optimization

## Related Concepts
- [[Content/Key Concepts/Tangency Portfolio|Tangency Portfolio]]
- [[Content/Key Concepts/Global Minimum Variance Portfolio|Global Minimum Variance Portfolio]]
- [[Content/Key Concepts/Efficient Frontier|Efficient Frontier]]
- [[Content/Key Concepts/Portfolio Constraints|Portfolio Constraints]]
- [[Content/Key Concepts/1-N Portfolio Strategy|1/N Portfolio Strategy]]