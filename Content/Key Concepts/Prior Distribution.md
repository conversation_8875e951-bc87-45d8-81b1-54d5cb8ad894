# Prior Distribution

The prior distribution is a key concept in Bayesian statistics that represents initial beliefs about parameters before observing data. In portfolio management, prior distributions provide a formal mechanism for incorporating economic theory, market equilibrium, expert opinion, and other sources of information into the estimation process.

## Key Points
- Represents initial beliefs about parameters before observing data
- Formalizes subjective information and domain knowledge
- Serves as a regularization mechanism to prevent overfitting
- Can range from highly informative to minimally informative
- Forms the starting point for Bayesian updating

## Types of Prior Distributions

### Informative Priors
- Incorporate specific beliefs about parameters
- Typically have concentrated probability mass
- Examples in finance:
  - CAPM-based priors for expected returns
  - Market-implied volatilities for variance parameters
  - Factor model structures for correlations

### Non-informative (Diffuse) Priors
- Aim to let the data dominate the posterior
- Typically have widely spread probability mass
- Examples:
  - Uniform distributions over plausible ranges
  - Jeffreys priors that are invariant to parameterization
  - Reference priors that maximize expected information gain

### Conjugate Priors
- Mathematically convenient priors that yield closed-form posteriors
- The posterior distribution has the same functional form as the prior
- Common examples in finance:
  - Normal prior for mean with known variance
  - Inverse Gamma prior for variance
  - Inverse Wishart prior for covariance matrices

### Hierarchical Priors
- Model parameters at multiple levels
- Allow for partial pooling of information across assets
- Particularly useful for large asset universes
- Examples:
  - Factor models with priors on factor loadings
  - Industry-grouped priors for sector effects
  - Time-varying parameter models

## Prior Specification in Portfolio Management

### Expected Returns
Common priors for expected returns include:
- Normal distribution centered on CAPM or factor model predictions
- Shrinkage toward grand mean or zero
- Black-Litterman equilibrium returns
- Historical averages with appropriate scaling

### Covariance Matrices
Common priors for covariance matrices include:
- Inverse Wishart distribution
- Factor structures with priors on loadings and idiosyncratic variances
- Shrinkage toward structured targets (e.g., constant correlation)
- Hierarchical models for large dimensions

### Predictive Relationships
Common priors for regression coefficients include:
- Normal distributions centered at theoretical values
- Shrinkage toward zero (regularization)
- Mixture distributions for variable selection
- Time-varying parameter models

## Elicitation Methods

### Statistical Approaches
- Using historical data from similar contexts
- Meta-analysis of previous studies
- Empirical Bayes methods that estimate prior parameters from the data

### Expert Opinion
- Structured interviews with domain experts
- Quantifying qualitative views (as in Black-Litterman)
- Consensus methods for combining multiple expert opinions

### Market-Implied Information
- Extracting information from market prices
- Using option-implied volatilities
- Reverse engineering equilibrium expected returns

## Mathematical Examples

### Normal Prior for Expected Returns
A normal prior for the expected return $\mu$ of an asset:

$$\mu \sim N(\mu_0, \sigma_0^2)$$

Where:
- $\mu_0$ is the prior mean (e.g., CAPM prediction)
- $\sigma_0^2$ is the prior variance (reflecting confidence in $\mu_0$)

### Inverse Wishart Prior for Covariance
An Inverse Wishart prior for the covariance matrix $\Sigma$:

$$\Sigma \sim IW(v_0, S_0)$$

Where:
- $v_0$ is the degrees of freedom (larger values indicate stronger prior)
- $S_0$ is the scale matrix (prior estimate of $\Sigma$ multiplied by $v_0$)

### Hierarchical Prior for Factor Model
A hierarchical prior for a factor model:

$$r_{it} = \alpha_i + \beta_i f_t + \epsilon_{it}$$
$$\alpha_i \sim N(\mu_\alpha, \sigma_\alpha^2)$$
$$\beta_i \sim N(\mu_\beta, \Sigma_\beta)$$
$$\mu_\alpha \sim N(0, \tau_\alpha^2)$$
$$\mu_\beta \sim N(1, \tau_\beta^2)$$

This structure allows for sharing information across assets while maintaining flexibility.

## Practical Considerations

### Sensitivity Analysis
- Assess how results change with different prior specifications
- Identify which aspects of the prior are most influential
- Ensure robustness to reasonable variations in prior beliefs

### Prior-Data Conflict
- Recognize when the data strongly contradicts the prior
- Consider model misspecification or data quality issues
- Potentially revise prior beliefs or model structure

### Communication
- Clearly document prior choices and rationale
- Express priors in terms meaningful to stakeholders
- Acknowledge subjective elements in the analysis

## Advantages
- Provides a formal mechanism for incorporating domain knowledge
- Reduces overfitting in small samples
- Improves estimation in high-dimensional problems
- Allows for sequential updating as new information arrives
- Creates more stable and intuitive portfolio allocations

## Limitations
- Requires explicit specification of beliefs
- May be challenging to elicit from experts
- Can dominate the data in small samples
- May introduce bias if poorly specified
- Adds complexity to the modeling process

## Related Concepts
- [[Content/Key Concepts/Posterior Distribution|Posterior Distribution]]
- [[Content/Key Concepts/Bayesian Estimation|Bayesian Estimation]]
- [[Content/Key Concepts/Bayesian Portfolio Optimization|Bayesian Portfolio Optimization]]
- [[Content/Key Concepts/Black-Litterman Model|Black-Litterman Model]]
- [[Content/Key Concepts/Shrinkage Estimation|Shrinkage Estimation]]
- [[Content/Key Concepts/Parameter Uncertainty|Parameter Uncertainty]]

## References
- Berger, J. O. (2006). The case for objective Bayesian analysis. Bayesian Analysis, 1(3), 385-402.
- Pastor, L., & Stambaugh, R. F. (2000). Comparing asset pricing models: An investment perspective. Journal of Financial Economics, 56(3), 335-381.
- Avramov, D., & Zhou, G. (2010). Bayesian portfolio analysis. Annual Review of Financial Economics, 2, 25-47.
- Jacquier, E., & Polson, N. G. (2012). Asset allocation in finance: A Bayesian perspective. Oxford Handbook of Bayesian Econometrics, 752-785.
