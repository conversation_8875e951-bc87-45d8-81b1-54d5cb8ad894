# Dynamic Portfolio Choice

## Definition
Dynamic Portfolio Choice refers to the process of making sequential investment decisions over time, taking into account how current choices affect future investment opportunities. Unlike static or myopic approaches, dynamic portfolio choice explicitly considers the multi-period nature of investment problems and allows portfolio weights to vary optimally over time in response to changing market conditions.

## Key Points
- Accounts for time-varying investment opportunities
- Considers the entire path of future portfolio decisions
- Incorporates state variables that predict future returns
- Distinguishes between myopic demand and hedging demand
- Typically requires solving the Bellman equation

## Mathematical Framework
The dynamic portfolio choice problem can be formulated as maximizing expected utility of terminal wealth:

```
max_{x_t,...,x_{T-1}} E_t[U(W_T)]
```

Subject to the wealth dynamics:
```
W_{t+1} = W_t(1 + R_p,t+1) = W_t(1 + x_t'r_{t+1} + R_{f,t})
```

Where:
- x_t is the vector of portfolio weights at time t
- r_{t+1} is the vector of excess returns
- R_{f,t} is the risk-free rate
- W_t is wealth at time t
- U(·) is the utility function

## Bellman Equation Formulation
The dynamic problem can be solved recursively using the Bellman equation:

```
V(τ, W_t, z_t) = max_{x_t} E_t[V(τ-1, W_{t+1}, z_{t+1})]
```

Where:
- V(τ, W_t, z_t) is the value function representing maximum expected utility with τ periods remaining
- z_t is the vector of state variables that predict future returns
- The expectation is taken over the joint distribution of returns and state variables

## Optimal Portfolio Composition
The optimal dynamic portfolio can be decomposed into:

```
x_t* = Myopic Demand + Hedging Demand
```

Where:
- **Myopic Demand**: Maximizes expected utility over the next period only
- **Hedging Demand**: Accounts for the impact of current decisions on future investment opportunities

## Special Cases

### I.I.D. Returns
When returns are independent and identically distributed:
- Hedging demand is zero
- Dynamic solution equals myopic solution
- Portfolio weights are constant over time (for CRRA utility)

### Log Utility
With logarithmic utility (relative risk aversion = 1):
- Hedging demand is zero even with predictable returns
- Optimal strategy is myopic
- Portfolio weights depend only on current investment opportunities

### Mean-Reverting Returns
When returns exhibit mean reversion:
- Hedging demand typically increases allocation to risky assets
- Long-term investors hold more in risky assets than short-term investors
- Stocks become relatively safer over longer horizons

### Mean-Averting Returns
When returns exhibit momentum or persistence:
- Hedging demand typically decreases allocation to risky assets
- Long-term investors hold less in risky assets than short-term investors
- Stocks become relatively riskier over longer horizons

## Solution Methods
Dynamic portfolio choice problems typically require numerical methods:

- **Backward Recursion**: Solving the Bellman equation by working backward from the terminal period
- **Simulation Methods**: Generating sample paths of returns and state variables
- **Across-Path Regressions**: Approximating conditional expectations using regression techniques
- **Discretization**: Converting continuous state and action spaces to discrete grids
- **Approximate Dynamic Programming**: Using function approximation for the value function

## Applications
- **Strategic Asset Allocation**: Long-term allocation across major asset classes
- **Life-Cycle Investing**: Age-based investment strategies
- **Pension Fund Management**: Managing assets to meet future liabilities
- **Endowment Management**: Preserving capital while generating income
- **Wealth Management**: Tailoring investment strategies to changing client circumstances

## Related Concepts
- [[Content/Key Concepts/Bellman Equation|Bellman Equation]]
- [[Content/Key Concepts/Hedging Demand|Hedging Demand]]
- [[Content/Key Concepts/Numerical Methods for Dynamic Optimization|Numerical Methods for Dynamic Optimization]]
- [[Content/Key Concepts/Long-Term Investment Strategies|Long-Term Investment Strategies]]
- [[Content/Key Concepts/Conditional Moments|Conditional Moments]]