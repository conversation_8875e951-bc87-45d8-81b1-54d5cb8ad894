# Risk-Reward Timing

## Definition
Risk-Reward Timing is a portfolio construction strategy that allocates wealth proportional to the ratio of expected returns to variances (the reward-to-risk ratio) of individual assets. It balances return expectations with risk considerations while ignoring correlations between assets.

## Key Points
- Allocates proportional to the ratio of expected return to variance
- Considers both return and risk characteristics
- Ignores correlations between assets
- Simpler to implement than full mean-variance optimization
- Can be viewed as a simplified version of the Sharpe ratio maximization

## Mathematical Formulation
The weights in a risk-reward timing portfolio are given by:

```
w_i = (μ_i/σ_i²) / Σ(j=1 to N) (μ_j/σ_j²)
```

Where:
- w_i is the weight of asset i
- μ_i is the estimated expected return of asset i
- σ_i² is the estimated variance of asset i
- N is the number of assets

## Generalized Formulation
A more flexible approach introduces timing parameters to control the intensity of return and volatility timing:

```
w_i = (μ_i^η₁/σ_i²^η₂) / Σ(j=1 to N) (μ_j^η₁/σ_j²^η₂)
```

Where:
- η₁ controls sensitivity to expected returns (η₁ = 0 ignores returns)
- η₂ controls sensitivity to variances (η₂ = 0 ignores variances)
- Setting η₁ = 0 and η₂ = 1 gives volatility timing
- Setting η₁ = η₂ = 0 gives equal weighting (1/N portfolio)

## Theoretical Relationship to Mean-Variance Optimization
Risk-reward timing can be derived as the optimal solution to a mean-variance problem under the assumption of:
- Diagonal covariance matrix (no correlations)
- Quadratic utility function
- No constraints other than weights summing to 1

## Advantages
- **Balance**: Considers both return and risk, unlike volatility timing
- **Simplicity**: No need for matrix inversion
- **Adaptability**: Automatically adjusts to changing market conditions
- **Intuitive**: Aligns with investor preference for high return per unit of risk
- **Implementable**: Can be easily constrained (e.g., no short-selling)

## Limitations
- **Estimation Risk in Returns**: Highly sensitive to errors in expected return estimates
- **Ignores Correlations**: Doesn't account for diversification benefits
- **Potential Concentration**: May lead to concentrated positions in assets with high estimated reward-to-risk ratios
- **Suboptimal**: Not mean-variance efficient when correlations exist
- **Turnover**: May generate higher turnover than volatility timing due to changing return estimates

## Empirical Performance
Kirby and Ostdiek (2012) found that:
- Risk-reward timing can outperform equal weighting and volatility timing when return estimates are reliable
- Performance is more variable than volatility timing due to sensitivity to return estimation errors
- Can be improved by reducing the weight given to expected returns (using η₁ < 1)
- Often generates higher turnover than volatility timing

## Implementation Considerations
- **Return Estimation**: Methods for estimating expected returns (historical, factor models, etc.)
- **Variance Estimation**: Choice of lookback period and estimation technique
- **Rebalancing Frequency**: How often to update weights
- **Parameter Tuning**: Selecting appropriate values for η₁ and η₂
- **Constraints**: Imposing no-short-selling or other constraints

## Related Concepts
- [[Content/Key Concepts/Volatility Timing|Volatility Timing]]
- [[Content/Key Concepts/1-N Portfolio Strategy|1/N Portfolio Strategy]]
- [[Content/Key Concepts/Sharpe Ratio|Sharpe Ratio]]
- [[Content/Key Concepts/Parameter Uncertainty|Parameter Uncertainty]]
- [[Content/Key Concepts/Portfolio Constraints|Portfolio Constraints]]