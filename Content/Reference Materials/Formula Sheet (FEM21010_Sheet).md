# FEM21010 Portfolio Management - Formula Sheet Summary

This document summarizes key formulas and econometric methods from the FEM21010 Portfolio Management course, as presented in the formula sheet dated November, 2024. It is intended as a reference for relations that do not need to be memorized, focusing on application and interpretation rather than derivation.

## Framework from [[Kan-Zhao Framework|<PERSON><PERSON> and <PERSON> (2007)]]

Assuming excess returns are i.i.d. normal, the sample means and covariance matrix have the following distributions:
$$
\hat{\mu} \sim N(\mu, \Sigma/T)
$$
$$
\hat{\Sigma} \sim W_N(T-1, \Sigma)/T
$$
For $X \sim W_N(\nu, \Phi)$, $X^{-1} \sim W_N^{-1}(\nu, \Phi^{-1})$.

Key expected values:
$$
E[X] = \Phi\nu \quad (1)
$$
$$
E[X^{-1}] = \Phi^{-1}/(\nu-N-1) \quad (2)
$$
$$
E[\hat{\mu}'\Sigma^{-1}\hat{\mu}] = (T\mu'\Sigma^{-1}\mu+N)/T \quad (3)
$$
$$
E[(\Sigma^{-1/2}\hat{\Sigma}\Sigma^{-1/2})^{-2}] = \frac{T^2}{(T-2)(T-N-1)(T-N-2)(T-N-4)} I_N \quad (4)
$$

## Key results and equalities in Short-term allocation

For a [[Linear Factor Models for Portfolio Optimization|linear factor model]] for returns, e.g., $R_t - \iota R_f = a + Bf_t + \varepsilon_t$, the [[Inverse Covariance Matrix|inverse covariance matrix]] is:
$$
\Sigma^{-1} = \Sigma_{\varepsilon}^{-1} - \Sigma_{\varepsilon}^{-1}B(B'\Sigma_{\varepsilon}^{-1}B + \Sigma_f^{-1})^{-1}B'\Sigma_{\varepsilon}^{-1} \quad (5)
$$
For $N$ linear regressions $r_i = a + \sum_{j \neq i} \varphi_j r_j + \varepsilon_i$, the inverse covariance matrix is:
$$
\Sigma^{-1} = \begin{pmatrix}
V(\varepsilon_1)^{-1} & -\varphi_{1,2}V(\varepsilon_1)^{-1} & ... & -\varphi_{1,N}V(\varepsilon_1)^{-1} \\
-\varphi_{2,1}V(\varepsilon_2)^{-1} & V(\varepsilon_2)^{-1} & ... & -\varphi_{2,N}V(\varepsilon_2)^{-1} \\
. & . & . & . \\
. & . & . & . \\
. & . & . & . \\
-\varphi_{N,1}V(\varepsilon_1)^{-1} & -\varphi_{N,2}V(\varepsilon_2)^{-1} & ... & V(\varepsilon_N)^{-1}
\end{pmatrix} \quad (6)
$$
[[Eigenvalue Shrinkage|Eigenvalue decomposition]] of the covariance matrix:
$$
\Sigma = \Lambda D \Lambda' = \sum_{i=1}^N d_i \lambda_i \lambda_i' \quad (7)
$$

## Key results and equalities in Long-term allocation

Log-normal returns - Portfolio return approximation:
$$
r_{p,t+1} - r_{f,t+1} = \alpha_t'(r_{t+1} - r_{f,t+1}\iota) + \frac{1}{2}\alpha_t'\sigma_t^2 - \frac{1}{2}\alpha_t'\Sigma_t\alpha_t \quad (8)
$$
For log-normal distributions:
$$
\log E_t X_{t+1} = E_t \log X_{t+1} + \frac{1}{2} V_t \log X_{t+1} \quad (9)
$$
[[Bellman Equation|Bellman equation]] of the optimal path of [[Dynamic Portfolio Choice|dynamic weights]]:
$$
V(\tau, W_t, z_t) = \max_{x_t} E_t [ V(\tau-1, W_t(x_t'r_{t+1} + R_{f_t}), z_{t+1}) ] \quad (10)
$$
Dynamic weights in continuous time:
$$
x_t^* = -\frac{V_2(.)}{W_t V_{22}(.)}(\Sigma_{p_t})^{-1}\mu_{p_t} - (\Sigma_{p_t})^{-1}D_{p_t}\rho_t D_{z_t}' \frac{V_2(.)}{W_t V_{22}(.)} V_{23}(.) V_2(.) \quad (11)
$$

## Econometric methods and estimators

The sheet also lists relevant econometric tools discussed in lectures, along with corresponding lecture numbers and primary reading references (Brandt (2010), DeMiguel, Garlappi and Uppal (2009), Kirby and Ostdiek (2012), Ledoit-Wolf (2003, 2004), Goto and Xu (2015), van Binsbergen, and Brandt (2007), Brandt, Santa-Clara and Valkanov (2009), DeMiguel, Martin-Utrera, Nogales and Uppal (2020), Brandt and Santa-Clara (2006)).

| Method                                            | Lecture                                          | Reading       | Note                                                              |                                                         |
| ------------------------------------------------- | ------------------------------------------------ | ------------- | ----------------------------------------------------------------- | ------------------------------------------------------- |
| [[Linear Factor Models for Portfolio Optimization | Linear factor models]]                           | L1-L8         | Slides                                                            | Strict, approximate, (un)observed factors               |
| Bayes uninformative priors                        | L4                                               | Brandt (2010) | Interpret the posterior distribution                              |                                                         |
| [[Decision-Theoretic Approach                     | Decision theory]]                                | L4            | Brandt (2010)                                                     | Interpret the posterior distribution                    |
| [[James-Stein Estimator                           | James-Stein estimator]]                          | L5            | Brandt (2010)                                                     | Related reading in DeMiguel, Garlappi and Uppal (2009)  |
| Bayes informative prior                           | L5                                               | Brandt (2010) | Interpret the posterior distribution                              |                                                         |
| [[Portfolio Constraints                           | Short-selling constraints]]                      | L6 (L4)       | Brandt (2010)                                                     | Related reading in DeMiguel, Garlappi and Uppal (2009)  |
| [[Shrinkage Estimation                            | Ledoit-Wolf (2003) estimator]]                   | L6            | Brandt (2010)                                                     | More reading in Ledoit-Wolf (2003)                      |
| Pastor’s estimator                                | L6                                               | Brandt (2010) | More reading in DeMiguel, Garlappi and Uppal (2009)               |                                                         |
| [[Constrained Mean-Variance Allocation            | Constrained MV and timing portfolios]]           | L7            | Kirby and Ostdiek (2012)                                          | The Constrained MV allocation can be derived            |
| Ledoit-Wolf (2004)                                | L8                                               | Slides        | Eigenvalue representation and shrinkage                           |                                                         |
| [[POET                                            | POET]]                                           | L8            | Slides                                                            | Unobserved factors and conditional sparsity             |
| [[Nodewise Regression                             | Neighbourhood selection]]                        | L8            | Slides                                                            | Inverse covariance matrix, regression and sparsity      |
| [[Graphical Lasso                                 | Graphical lasso]]                                | L8            | Slides                                                            | More reading in Goto and Xu (2015)                      |
| [[Numerical Methods for Dynamic Optimization      | Numerical methods for dynamic portfolio choice]] | L10           | Slides                                                            | More reading in van Binsbergen, and Brandt (2007)       |
| [[Parametric Portfolio Policies                   | Parametric portfolio weights]]                   | L11           | Slides                                                            | More reading in Brandt, Santa-Clara and Valkanov (2009) |
| Regularized parametric portfolio weights          | L11                                              | Slides        | More reading in DeMiguel, Martin-Utrera, Nogales and Uppal (2020) |                                                         |
| Conditional parametric portfolio weights          | L11                                              | Slides        | More reading in Brandt and Santa-Clara (2006)                     |                                                         |

The literature listed in **Reading** is our primary reference for the respective methods.
- Linear factor models
- Bayes uninformative priors
- Decision theory
- James-Stein estimator
- Bayes informative prior
- Short-selling constraints
- Ledoit-Wolf (2003) estimator
- Pastor’s estimator
- Constrained MV and timing portfolios
- Ledoit-Wolf (2004)
- POET
- Neighbourhood selection
- Graphical lasso
- Numerical methods for dynamic portfolio choice
- Parametric portfolio weights
- Regularized parametric portfolio weights
- Conditional parametric portfolio weights
