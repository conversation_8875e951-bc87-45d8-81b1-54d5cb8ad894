# Week 5 Exercises (Exercises_Week_5)

## Long-Term Investment Strategies

This document covers portfolio allocation strategies for long-term investments, focusing on buy-and-hold allocations with iid and non-iid returns, and dynamic portfolio allocation.

### Key Concepts

- Comparison of myopic, constant proportion, buy-and-hold, and dynamic allocation strategies
- Analysis of portfolio return ($r_{p,t \to t+2}$) and variance ($\sigma^2_{p,t \to t+2}$) over a two-period horizon
- Expected return ($\mu$) and volatility ($\sigma^2$) of the risky asset
- Wealth allocation ($\alpha$) to the risky asset
- Power utility with risk aversion ($\gamma$)

### Mathematical Equations and Models

1. **Myopic Portfolio Solution (iid returns)**

   $$ \alpha_t = \frac{\mu - r_f + \sigma^2/2}{\gamma \sigma^2} $$

2. **Buy-and-Hold Optimization (Lognormal Returns)**

   Maximizing expected utility is equivalent to maximizing:

   $$ E_t r_{p,t \to t+2} + \frac{1}{2}(1-\gamma)\sigma^2_{p,t \to t+2} $$

3. **Restricted VAR(1) Model (Non-iid returns)**

   For log return ($r_t$) and log dividend-to-price ratio ($x_t$):

   $$ \begin{pmatrix} r_{t+1} \\ x_{t+1} \end{pmatrix} = \beta_0 + \beta_1 x_t + \epsilon_{t+1}, \quad \epsilon_{t+1} \sim N(0, \Sigma), \text{iid}. $$

   Where $\beta_0$ is a 2x1 vector, $\beta_1$ is a 2x1 vector, $\epsilon_{t+1}$ is a 2x1 error vector, and $\Sigma$ is the 2x2 covariance matrix:

   $$ \Sigma = \begin{pmatrix} \sigma^2_1 & \sigma_{1,2} \\ \sigma_{1,2} & \sigma^2_2 \end{pmatrix} $$

   A claim regarding the variance of the sum of returns is presented and needs to be shown incorrect.

4. **Dynamic Portfolio Allocation Model**

   With risky asset log return ($r_t$), log dividend-to-price ratio ($x_t$), and risky benchmark asset log return ($r_{0,t+1}$):

   $$ r_{t+1} - r_{0,t+1} = x_t + u_{t+1} $$
   $$ x_{t+1} = \phi x_t + \omega_{t+1} $$
   $$ r_{0,t+1} = e_{t+1} $$

   Where $u_t, \omega_t, e_t$ are iid normally distributed error terms with zero mean and variances $\sigma^2_u, \sigma^2_\omega, \sigma^2_e$, and covariances $\sigma_{u,\omega}, \sigma_{u,e}, \sigma_{\omega,e}$.

5. **Optimal Weight $\alpha_t$ in the First Period (Dynamic Allocation)**

   Given a constant risky asset weight $\alpha^*_{t+1}$ in the second period:

   $$ \alpha_t = \frac{1}{\gamma} \frac{x_t + \sigma^2_u/2}{\sigma^2_u} + (1-\frac{1}{\gamma})\alpha^*_{t+1}\left(-\frac{\sigma_{u,\omega}}{\sigma^2_u}\right) + (1-\frac{1}{\gamma})\left(-\frac{\sigma_{u,e}}{\sigma^2_u}\right) $$

   Economic explanations are required for the terms in this equation.

6. **Continuous Time Solution Formula**

   For an expected utility maximizer over $K$ periods:

   $$ \alpha^*_t = \frac{1}{\gamma}(\Sigma^p_t)^{-1}\mu^p_t + \frac{\beta_{zr}\eta}{\gamma} $$

   This formula is used to argue when long-term and short-term portfolio choices are equal.