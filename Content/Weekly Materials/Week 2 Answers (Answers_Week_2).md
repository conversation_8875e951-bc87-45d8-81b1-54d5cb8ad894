# Week 2 Answers (Answers_Week_2)

## Solutions and Explanations

This document provides solutions and explanations for exercises related to Week 2, covering topics from a regression perspective, hedging portfolios, and the <PERSON><PERSON> and <PERSON><PERSON><PERSON> experiment.

### Regression Perspective

- Derivation of the OLS estimator $\hat{b} = (Y'Y)^{-1}Y'1$ and covariance matrix $\hat{\Sigma} = T^{-1}Y'Y - \bar{y}\bar{y}'$.
- Shows that $\hat{b} = (\hat{\Sigma} + \bar{y}\bar{y}')^{-1}\bar{y}$.
- Using a hint, the estimator is simplified to $\hat{b} = \hat{\Sigma}^{-1}\bar{y}c$, where $c$ is a scalar.
- Derivation of $\hat{b}'\iota'\hat{b} = \hat{\Sigma}^{-1}\bar{y}\iota'\hat{\Sigma}^{-1}\bar{y}$.

### Hedging Portfolio

- Derivation of optimal portfolio weights $w$ by maximizing $w'b$ subject to $w'\iota = 0$ and $w'\Sigma w = \sigma_h^2$ using a Lagrangian.
- First-order conditions are $b - \lambda_1\iota - 2\lambda_2\Sigma w = 0$, $w'\iota = 0$, and $w'\Sigma w - \sigma_h^2 = 0$.
- Solving the FOCs leads to $\lambda_1 = A = b'\Sigma^{-1}\iota / \iota'\Sigma^{-1}\iota$ and an expression for $\lambda_2$.
- The optimal weights are derived as $w = \sigma_h \Sigma^{-1}(b-A\iota) / \sqrt{(b-A\iota)'\Sigma^{-1}(b-A\iota)}$.

### Jobson and Korkie Experiment

- Describes a four-step simulation experiment to assess the economic performance loss from estimation error in mean and covariance matrix.
- Steps involve estimating moments, simulating returns, forming hypothetical portfolios, and evaluating performance.
- Findings indicate that plug-in weights based on in-sample estimates do not closely span the mean-variance frontier, even with a relatively long time series (T=150).
- Discusses the bias-variance trade-off when restricting portfolio weights, suggesting that constraints can improve financial performance by reducing variance, leading constrained estimates to better span the mean-variance frontier.