# Week 3 Answers (Answers_Week_3)

## Portfolio Management Solutions

This document discusses exercises related to portfolio management, focusing on biased weights, parameter uncertainty, and empirical implementation.

### Biased Weights

For investors with mean-variance preferences and a risk-free rate, the optimization problem is:

$$\max_w R_f + w'\mu - \frac{\gamma}{2} w'\Sigma w$$

The first-order condition is $\mu - \gamma\Sigma w = 0$, leading to the optimal weight $w^* = \frac{1}{\gamma}\Sigma^{-1}\mu$.

Using plug-in estimates $\hat{w} = \gamma^{-1}\hat{\Sigma}^{-1}\hat{\mu}$, the expected value is $E[\hat{w}] = w^* \frac{T}{T-N-2}$. This shows an over-investment in risky assets, which tends towards $w^*$ as $T$ grows large.

### Parameter Uncertainty

The expected utility evaluated at sample moment plug-in estimates is derived. The derivation involves the expected value of the inverse Wishart distribution for $\hat{\Sigma}^{-1}$ and the distribution of the expected squared Sharpe ratio. The final expression for expected utility is:

$$E[U(\hat{w})] = \frac{1}{2\gamma} \mu'\Sigma^{-1}\mu k - \frac{NT(T-2)}{2\gamma(T-N-1)(T-N-2)(T-N-4)}$$

where $k = \frac{T}{T-N-2}(2 - \frac{T(T-2)}{(T-N-1)(T-N-4)})$.

The expected loss $E[L(w^*, \hat{w})] = (w^{*'}\mu - \frac{\gamma}{2} w^{*'}\Sigma w^*) - E[U(\hat{w})]$ is derived as:

$$E[L(w^*, \hat{w})] = \frac{1}{2\gamma} \mu'\Sigma^{-1}\mu(1-k) + \frac{NT(T-2)}{2\gamma(T-N-1)(T-N-2)(T-N-4)}$$

### Empirical Implementation

- Target weights for the optimal portfolio do not change over time, but realized weights do due to varying realized returns, leading to low turnover.
- The tangency portfolio weights are prone to extreme and unstable values due to the imprecision of estimates for expected returns and the covariance matrix, resulting in high turnover.
- Value-weighted market portfolio weights depend on market capitalization, ensuring the difference between realized and desired weights is always zero.
- The impact of estimation error on other portfolio rules is less significant when $M$ (number of observations) is large or $N$ (number of assets) is small.
- High idiosyncratic volatility increases the benefit of optimal diversification over naive diversification (1/N portfolio).
- Imposing weight restrictions is equivalent to shrinking the covariance matrix, particularly for assets where constraints are binding. This can improve performance by restricting the impact of estimation error.
- If an upper bound constraint for asset $j$ holds, its KKT multiplier $\delta_j$ is positive. This alters the estimated variance and covariances involving asset $j$:

$$\tilde{\sigma}^2_{j,j} = \sigma^2_{j,j} + 2\delta_j$$
$$\tilde{\sigma}^2_{j,i} = \sigma^2_{j,i} + \delta_j + \delta_i$$

This makes asset $j$ less attractive, lowering its weight.