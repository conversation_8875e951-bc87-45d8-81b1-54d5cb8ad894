# Week 4 Answers (Answers_Week_4)

## Expected Losses, Shrinkage Estimation, and Implementation

This document covers expected losses in portfolio allocation, shrinkage estimation, and empirical implementation strategies.

### Expected Losses

- The loss function is defined as $L(w^*, c\iota) = U(w^*) - U(c\iota)$, where $w^*$ is the optimal allocation and $c\iota$ is a constant allocation.
- The optimal constant $c^*$ is derived as $c^* = \frac{\iota'\mu}{\gamma\iota'\Sigma\iota}$.
- The loss evaluated at $c^*$ is $L(w^*, c^*\iota) = \frac{1}{2\gamma}\mu'\Sigma^{-1}\mu - \frac{1}{2}\frac{(\iota'\mu)^2}{\gamma\iota'\Sigma\iota}$.
- This loss can be expressed in terms of Sharpe ratios for the optimal allocation ($SR^2_{w^*}$) and the 1/N allocation ($SR^2_{1/N}$) as $L(w^*, c^*\iota) = \frac{1}{2\gamma}(SR^2_{w^*} - SR^2_{1/N})$.
- The expected loss for the allocation $\hat{w} = \frac{1}{\gamma}\Sigma^{-1}\hat{\mu}$ is $E[L(w^*, \hat{w})|\Sigma] = \frac{N}{2\gamma T}$.
- The condition for $\hat{w}$ to have a lower expected loss than $c^*\iota$ is $SR^2_{w^*} - SR^2_{1/N} - \frac{N}{T} > 0$.

### Shrinkage Estimation

- The shrinkage estimator is $w_{sh} = \alpha w_0 + (1-\alpha)\hat{w}$, where $w_0$ is a target portfolio and $\hat{w}$ is an estimated optimal portfolio.
- The optimal shrinkage intensity $\alpha$ is derived by maximizing the expected utility $E(U(w_{sh}))$. The formula for $\alpha$ involves expected values of variances and covariances of the portfolios.
- Shrinkage estimators combine an unbiased vector (like $\hat{w}$) with a biased target (like $w_0$) that has low variance. The equally weighted portfolio (1/N) is a common biased target with no estimation variance.
- The expected portfolio variance of $\hat{w}$ is derived using properties of sample moments and matrix algebra, resulting in a complex expression involving $T$, $N$, $\gamma$, $\mu$, and $\Sigma$.

### Empirical Implementation

- The Optimal Combination (OC) allocation combines the tangency and minimum variance allocations, calibrated using the estimated expected return on the equally weighted portfolio. This makes it less sensitive to outliers and improves out-of-sample performance.
- An allocation that omits covariances is an extreme form of shrinkage. It introduces bias but reduces estimation uncertainty variance. It is well-defined even when $N > T$ and results in non-negative weights.
- The return-loss k metric, defined as $\frac{\mu_{1/N}}{\sigma_{1/N}} \times \sigma_k - \mu_k < 0$, indicates when the Sharpe ratio of the k-th portfolio is greater than that of the 1/N allocation. Its value represents the additional expected return needed for the k-th and 1/N portfolios to have equivalent Sharpe ratios.