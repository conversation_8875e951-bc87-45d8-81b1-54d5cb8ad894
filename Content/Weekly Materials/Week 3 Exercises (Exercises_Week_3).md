# Week 3 Exercises (Exercises_Week_3)

## Portfolio Management Topics

This document provides exercises for week 3, covering topics in portfolio management.

### Key Concepts

#### Biased Weights

Discusses how using sample plug-in estimators for mean-variance portfolio allocation leads to biased but consistent weights, often resulting in over-investment in risky assets.

#### Parameter Uncertainty

Examines the impact of uncertainty in estimating expected returns ($\mu$) and covariance matrix ($\Sigma$) on portfolio performance.

- Quadratic utility function: $U(w) = w'\mu - \frac{\gamma}{2}w'\Sigma w$
- Loss function: $L(w^*, w) = U(w^*) - U(w)$
- Mean-variance optimal weights: $w^* = \gamma^{-1}\Sigma^{-1}\mu$
- Distributions of sample estimators: $\hat{\mu} \sim N(\mu, \Sigma/T)$ and $\hat{\Sigma} \sim W_N(T-1, \Sigma)/T$
- Includes complex expected value formulas for $E[U(\hat{w})]$ and $E[L(w^*, \hat{w})]$ involving parameters like $T$, $N$, $\gamma$, $\mu$, and $\Sigma$.

#### Empirical Implementation

References studies on portfolio performance and turnover.

- Discusses turnover for strategies like 1/N, mean-variance, and value-weighted market portfolios.
- Explores the relative performance of the 1/N strategy under varying parameters (estimation window, number of assets, idiosyncratic volatility).
- Explains how weight restrictions improve Markowitz portfolio performance, referencing the altered covariance matrix formula $\tilde{S} = S + (\delta\iota' + \iota\delta') - (\lambda\iota' + \iota\lambda')$.
