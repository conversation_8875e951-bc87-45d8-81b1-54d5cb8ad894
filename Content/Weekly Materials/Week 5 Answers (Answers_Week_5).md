# Week 5 Answers (Answers_Week_5)

## Long-Term Investment Strategies

This document provides solutions and derivations for portfolio allocation problems discussed in Week 5, focusing on buy-and-hold and dynamic strategies under different assumptions about asset returns.

### Buy and Hold Allocations with iid Returns

The portfolio return is approximated as:
$$r_{p,t \to t+2} - r_{f,t \to t+2} \approx \alpha_t (r_{t \to t+2} - r_{f,t \to t+2}) + \frac{1}{2} (1-\alpha_t)\alpha_t \sigma^2_{t \to t+2}$$
The investor maximizes expected utility, leading to the optimization problem:
$$max_{\alpha_t} E_t r_{p,t \to t+2} + \frac{1}{2}(1-\gamma)\sigma^2_{p,t \to t+2}$$
The first-order condition (FOC) is:
$$E_t (r_{t \to t+2} - r_{f,t \to t+2}) + \frac{1}{2} \sigma^2_{t \to t+2} - \gamma \alpha_t \sigma^2_{t \to t+2} = 0$$
Solving for the optimal weights $\alpha_t$:
$$\alpha_t = \frac{E_t (r_{t \to t+2} - r_{f,t \to t+2}) + \frac{1}{2} \sigma^2_{t \to t+2}}{\gamma \sigma^2_{t \to t+2}}$$
If returns are iid, $E_t(r_{t \to t+2}) = 2\mu$, $E_t(r_{f,t \to t+2}) = 2r_f$, and $\sigma^2_{t \to t+2} = 2\sigma^2$. Substituting these gives the myopic investment allocation:
$$\alpha_t = \frac{2\mu - 2r_f + \sigma^2}{\gamma 2\sigma^2} = \frac{\mu - r_f + \frac{1}{2}\sigma^2}{\gamma \sigma^2}$$

### Buy and Hold Allocations with Non-iid Returns

For non-iid returns, the returns are modeled as:
$$r_{t+1} = \beta_{1,0} + \beta_{1,1} x_t + \epsilon_{1,t+1}$$
$$r_{t+2} = \beta_{1,0} + \beta_{1,1} x_{t+1} + \epsilon_{1,t+2}$$
where $x_{t+1} = \beta_{2,0} + \beta_{2,1} x_t + \epsilon_{2,t+1}$.
The sum of returns is:
$$r_{t+1} + r_{t+2} = 2\beta_{1,0} + \beta_{1,1} \beta_{2,0} + \beta_{1,1}(1+\beta_{2,1})x_t + \epsilon_{1,t+1} + \beta_{1,1} \epsilon_{2,t+1} + \epsilon_{1,t+2}$$
The conditional variances are:
$$V_t(r_{t+1}) = V(\epsilon_{1,t+1}) = \sigma^2_{1,1}$$
$$V_t(r_{t+1} + r_{t+2}) = V_t(\epsilon_{1,t+1} + \beta_{1,1} \epsilon_{2,t+1} + \epsilon_{1,t+2}) = 2\sigma^2_{1,1} + \beta^2_{1,1} \sigma^2_{2,2} + 2\beta_{1,1} \sigma_{1,2}$$
The conditional expectation is:
$$E_t(r_{t \to t+2}) = E_t(r_{t+1} + r_{t+2}) = 2\beta_{1,0} + \beta_{1,1} \beta_{2,0} + \beta_{1,1}(1+\beta_{2,1})x_t$$
The optimal buy-and-hold portfolio allocation is:
$$\alpha = \frac{2\beta_{1,0} + \beta_{1,1} \beta_{2,0} + \beta_{1,1}(1+\beta_{2,1})x_t - 2r_f + \frac{1}{2}(2\sigma^2_{1,1} + \beta^2_{1,1} \sigma^2_{2,2} + 2\beta_{1,1} \sigma_{1,2})}{\gamma(2\sigma^2_{1,1} + \beta^2_{1,1} \sigma^2_{2,2} + 2\beta_{1,1} \sigma_{1,2})}$$

### Dynamic Portfolio Allocation

The investor solves:
$$max E_t \frac{W^{1-\gamma}_{t+2}}{1-\gamma}$$
s.t.
$$W_{t+2} = W_t (1+R_{p,t+1})(1+R_{p,t+2})$$
Using log approximations, the problem becomes:
$$max E_t (r_{p,t+1} + r_{p,t+2}) + \frac{1}{2}(1-\gamma)V_t(r_{p,t+1} + r_{p,t+2})$$
The log portfolio return approximation is:
$$r_{p,t+1} - r_{0,t+1} = \alpha_t (r_{t+1} - r_{0,t+1}) + \frac{1}{2} \alpha_t (1-\alpha_t) \sigma^2_u$$
The expected value of cumulative log returns is:
$$E_t(r_{p,t+1} + r_{p,t+2}) = \alpha_t x_t + \frac{1}{2} \alpha_t (1-\alpha_t) \sigma^2_u + \alpha^*_{t+1} \varphi x_t + \frac{1}{2} \alpha^*_{t+1} (1-\alpha^*_{t+1}) (\sigma^2_w + \sigma^2_u)$$
The portfolio variance is:
$$V_t(r_{p,t+1} + r_{p,t+2}) = \sigma^2_e + \sigma^2_e + \alpha^2_t \sigma^2_u + \alpha^{*2}_{t+1} (\sigma^2_\omega + \sigma^2_u) + 2\alpha_t \sigma_{u,e} + 2\alpha^*_{t+1} \sigma_{e,\omega} + 2\alpha^*_{t+1} \sigma_{e,u} + 2\alpha_t \alpha^*_{t+1} \sigma_{u,\omega}$$
The FOC for dynamic allocation is:
$$x_t + \sigma^2_u/2 - \gamma \sigma^2_u \alpha_t + (1-\gamma)\sigma_{u,e} + (1-\gamma)\alpha^*_{t+1} \sigma_{u,\omega} = 0$$
Solving for the optimal dynamic allocation $\alpha_t$:
$$\alpha_t = \frac{x_t + \sigma^2_u/2 + (1-\gamma)\sigma_{u,e} + (1-\gamma)\alpha^*_{t+1} \sigma_{u,\omega}}{\gamma \sigma^2_u} = \frac{1}{\gamma} \frac{x_t + \sigma^2_u/2}{\sigma^2_u} + (1-\frac{1}{\gamma})\alpha^*_{t+1} (-\frac{\sigma_{u,\omega}}{\sigma^2_u}) + (1-\frac{1}{\gamma})(-\frac{\sigma_{u,e}}{\sigma^2_u})$$
The document also briefly discusses the intuition behind the hedge demand terms related to $\sigma_{u,\omega}$ and $\sigma_{u,e}$.