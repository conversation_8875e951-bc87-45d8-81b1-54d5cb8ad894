# Week 4 Exercises (Exercises_Week_4)

## Expected Losses and Shrinkage Estimation

This document covers two main topics: Expected losses and Shrinkage estimation in portfolio management, along with a section on empirical implementation.

### Expected Losses

- **Quadratic Utility Function:** $U(w) = w'\\mu - \\frac{\\gamma}{2} w'\\Sigma w$
- **Loss Function:** $L(w^*, w) = U(w^*) - U(w)$
- **Optimal Mean-Variance Weights:** $w^* = \\gamma^{-1}\\Sigma^{-1}\\mu$
- Assumes iid normal returns.
- Distributions for estimates: $\\hat{\\mu} \\sim N(\\mu, \\Sigma/T)$, $\\hat{\\Sigma} \\sim W_N(T-1,\\Sigma)/T$.
- Analysis of a non-optimal portfolio $w = c\\iota$.
- Derivations related to minimizing out-of-sample loss and comparing losses of different allocations, including the condition $SR^2_{w^*} - SR^2_{1/N} - \\frac{N}{T} > 0$.
- Hint provided on the distribution and expected value of $\\hat{\\mu}'\\Sigma^{-1}\\hat{\\mu} \\sim \\chi^2_N(T\\mu'\\Sigma^{-1}\\mu)/T$, with $E[\\hat{\\mu}'\\Sigma^{-1}\\hat{\\mu}] = (T\\mu'\\Sigma^{-1}\\mu+N)/T$.

### Shrinkage Estimation

- Discusses shrinking sample-based portfolio weights $\\hat{w}$ towards target weights $w^0$.
- **Shrinkage Weights:** $w^{sh} = \\alpha w^0 + (1-\\alpha)\\hat{w}$
- Derivation of the optimal shrinkage intensity $\\alpha = \\frac{E(\\hat{\\sigma}^2) - E(\\sigma_{0,s}) - \\frac{1}{\\gamma}(E(\\hat{\\mu}) - E(\\mu_0))}{E(\\sigma^2_0) + E(\\hat{\\sigma}^2) - 2E(\\sigma_{0,s})}$.
- Hint provided: $\\sigma_{0,s} = w_0'\\Sigma \\hat{w}$.
- Question on proposing an empirically viable target portfolio $w^0$.
- Assumes iid normal returns and provides distributions for sample moments.
- Plug-in portfolio: $\\hat{w} = \\gamma^{-1}\\hat{\\Sigma}^{-1}\\hat{\\mu}$.
- Derivation of $E(\\hat{\\sigma}^2)$.
- Hints provided for expected values involving $\\tilde{\\Sigma}^{-2}$ and $\\hat{\\mu}'\\Sigma^{-1}\\hat{\\mu}$.

### Empirical Implementation

- References Kirby and Ostdiek (2012) and DeMiguel, Garlappi and Uppal (2009).
- Discusses Tangency Portfolio (TP), Global Minimum Variance (MV), and Optimal Constrained (OC) allocations.
- **Optimal Constrained Allocation:** $w^{OC} = \\alpha w^{tan} + (1-\\alpha)w^{gmv}$ with a detailed formula.
- Target excess return for $w^{OC}$ is the estimated excess return on the 1/N portfolio.
- Mentions out-of-sample Sharpe ratios ($\\hat{\\lambda}_p$) and Turnover ($\\hat{\\tau}_p$).
- Question on why OC has higher Sharpe ratios than TP.
- **Volatility Timing Allocation:** $w_{i,vol} = \\frac{(1/\\sigma^2_i)^\\eta}{\\eta \\sum_i^N (1/\\sigma^2_i)^\\eta}$.
- Questions on benefits/drawbacks of volatility timing and the effect of $\\eta=0$ or $\\eta \\to \\infty$.
- Discussion of trading costs and **Return-Loss:** return-loss$_k = \\frac{\\mu_{1/N}}{\\sigma_{1/N}} \\times \\sigma_k - \\mu_k$.
- Questions on interpreting return-loss$_k$ and return-loss$_k < 0$.