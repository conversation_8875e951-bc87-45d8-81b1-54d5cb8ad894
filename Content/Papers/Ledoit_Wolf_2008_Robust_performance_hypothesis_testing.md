# Robust performance hypothesis testing with the Sharpe ratio

**Authors: <AUTHORS>
**Journal:** Journal of Empirical Finance
**Year:** 2008
**DOI:** [https://doi.org/10.1016/j.jempfin.2008.03.002](https://doi.org/10.1016/j.jempfin.2008.03.002)

## Abstract

Applied researchers often test for the difference of the Sharpe ratios of two investment strategies. A very popular tool to this end is the test of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (1981), which has been corrected by <PERSON><PERSON><PERSON> (2003). Unfortunately, this test is not valid when returns have tails heavier than the normal distribution or are of time series nature. Instead, we propose the use of robust inference methods. In particular, we suggest to construct a studentized time series bootstrap confidence interval for the difference of the Sharpe ratios and to declare the two ratios different if zero is not contained in the obtained interval. This approach has the advantage that one can simply resample from the observed data as opposed to some null-restricted data. A simulation study demonstrates the improved finite sample performance compared to existing methods. In addition, two applications to real data are provided.

## Key Concepts and Findings

*   **Problem with Standard Tests:** The widely used test by [[Jobson-Korkie Experiment|<PERSON><PERSON> and <PERSON><PERSON> (1981)]], even with <PERSON><PERSON><PERSON>'s (2003) correction, is invalid for comparing [[Sharpe Ratio|Sharpe ratios]] when returns are not normally distributed (heavy tails) or exhibit time series characteristics (autocorrelation, volatility clustering).
*   **Robust Inference Methods:** The paper proposes using robust methods for more generally valid inference.
    *   **[[HAC Inference|HAC (Heteroskedasticity and Autocorrelation Consistent)]] Standard Errors:** This approach works asymptotically but can be liberal (reject a true null hypothesis too often) in small to moderate sample sizes.
    *   **Studentized Time Series [[Bootstrap Methods|Bootstrap]]:** This is suggested as an improved alternative, particularly for finite samples. It involves resampling blocks of data to capture the time series dependence.
*   **Studentized Bootstrap Approach:** The proposed method constructs a studentized time series bootstrap confidence interval for the difference in Sharpe ratios. If the interval does not contain zero, the null hypothesis of equal Sharpe ratios is rejected. This avoids resampling under a null hypothesis constraint.
*   **Simulation Results:** Simulations show that the Jobson-Korkie/Memmel test and standard HAC inference can be liberal under non-normal or time series data. The studentized time series bootstrap (Boot-TS) performs well in controlling the rejection probability under the null hypothesis for both i.i.d. and time series data.
*   **Empirical Applications:** Applications to mutual fund and hedge fund data illustrate that the standard JKM test may indicate a significant difference in Sharpe ratios, while the more robust HAC and Boot-TS methods do not, especially for hedge funds which often exhibit stronger time series properties.
*   **Limitations of Other Bootstrap Methods:** The paper notes limitations in previous bootstrap proposals for Sharpe ratio inference, such as using only i.i.d. bootstrap for time series data or using non-studentized bootstrap which does not improve inference accuracy over standard asymptotic methods.

## LaTeX Formatting Examples (from the paper)

*   Mean vector and covariance matrix:
    \[
    \mu = \begin{pmatrix} \mu_i \\ \mu_n \end{pmatrix} \quad \text{and} \quad \Sigma = \begin{pmatrix} \sigma_i^2 & \sigma_{in} \\ \sigma_{in} & \sigma_n^2 \end{pmatrix}
    \]
*   Difference between Sharpe ratios:
    \[
    \Delta = \text{Sh}_i - \text{Sh}_n = \frac{\mu_i}{\sigma_i} - \frac{\mu_n}{\sigma_n}
    \]
*   Estimator of the difference:
    \[
    \hat{\Delta} = \hat{\text{Sh}}_i - \hat{\text{Sh}}_n = \frac{\hat{\mu}_i}{\hat{\sigma}_i} - \frac{\hat{\mu}_n}{\hat{\sigma}_n}
    \]
*   Function for the difference:
    \[
    f(a,b,c,d) = \frac{a}{\sqrt{c - a^2}} - \frac{b}{\sqrt{d - b^2}}
    \]
*   Asymptotic distribution:
    \[
    \sqrt{T}(\hat{\upsilon} - \upsilon) \xrightarrow{d} N(0, \Psi)
    \]
*   Gradient vector:
    \[
    \nabla f(a,b,c,d) = \begin{pmatrix}
    (c - a^2)^{-0.5} \\
    -(d - b^2)^{-0.5} \\
    \frac{1}{2} a (c - a^2)^{-1.5} \\
    -\frac{1}{2} b (d - b^2)^{-1.5}
    \end{pmatrix}
    \]
*   Bootstrap p-value formula:
    \[
    PV = \frac{\#\{\tilde{d}^*_m \ge d\} + 1}{M + 1}
    \]

## Connections to Existing Notes

*   [[Sharpe Ratio]]
*   [[Bootstrap Methods]]
*   [[HAC Inference]]
*   [[Jobson-Korkie Experiment]]