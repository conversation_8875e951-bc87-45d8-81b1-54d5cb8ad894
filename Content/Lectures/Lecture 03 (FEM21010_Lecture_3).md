# Lecture 03 (FEM21010_Lecture_3)

## Regression Perspective and Testing Portfolio Performance

This lecture explores how portfolio optimization and performance testing can be viewed and conducted using regression analysis.

### The Inverse Covariance Matrix from a Regression Perspective

The inverse covariance matrix ([[Content/Key Concepts/Inverse Covariance Matrix|$\Sigma^{-1}$]]) is fundamental to many portfolio allocations ([[Content/Key Concepts/Global Minimum Variance Portfolio|GMV]], [[Content/Key Concepts/Tangency Portfolio|Tangency]], [[Content/Key Concepts/Efficient Frontier|Efficient Frontier]]). It can be understood through a system of linear regressions.

For $N$ assets, consider regressing each asset's return ($r_i$) on all other asset returns ($r_j$ for $j \neq i$):

$$r_i = a + \sum_{j \neq i} \phi_j r_j + \epsilon_i$$

The elements of the inverse covariance matrix $\Sigma^{-1}$ are related to the coefficients ($\phi_{i,j}$) and residual variances ($V(\epsilon_i)$) from these regressions:

*   Diagonal elements: $$\theta_{i,i} = V(\epsilon_i)^{-1}$$
*   Off-diagonal elements: $$\theta_{i,j} = -\phi_{i,j} V(\epsilon_i)^{-1}$$

$V(\epsilon_i)$ represents the non-diversifiable risk of asset $i$ given a portfolio of the other $N-1$ assets. The coefficients $\phi_{i,j}$ relate to the optimal hedging portfolio weights of asset $j$ with respect to asset $i$.

This regression representation allows for estimating the inverse covariance matrix without direct matrix inversion, although ensuring the resulting matrix is symmetric and positive semi-definite can be challenging.

### Regression Approaches to Portfolio Choice

Empirical portfolio weights can be computed as parameter estimates from linear regression models. This offers advantages:

*   Utilize tools from regression analysis (standard errors, tests, goodness of fit).
*   Easily compare different models.
*   Gain insights into empirical issues.
*   Straightforward extension to other estimation approaches ([[Content/Key Concepts/Shrinkage Estimation|shrinkage]], Bayes).

**[[Content/Key Concepts/Britten-Jones Regression|Britten-Jones (1999) Regression]] for Tangency Portfolio:**

The tangency portfolio weights ($w_{TAN}$) can be obtained from the OLS estimate ($\hat{b}$) of regressing a vector of ones ($\iota$) on the matrix of excess returns ($R$):

$$\iota = b R_t + u_t$$

$$w_{TAN} = \frac{\hat{b}}{\iota' \hat{b}}$$

This seemingly strange regression allows for standard inference on tangency portfolio weights.

**[[Content/Key Concepts/Kempf-Memmel Regression|Kempf-Memmel (2006) Regression]] for GMV Portfolio:**

The GMV portfolio weights can be obtained from the OLS regression of the first asset's return ($r_{t1}$) on the differences between the first asset's return and all other asset returns ($r_{t1} - r_{tj}$ for $j=2,...,N$):

$$r_{t1} = \mu_p + \sum_{j=2}^N w_j (r_{t1} - r_{tj}) + \epsilon_t$$

Where $\hat{w}_j$ are the estimated GMV weights for $j=2,...,N$, and $\hat{w}_1 = 1 - \sum_{j=2}^N \hat{w}_j$.

### Testing Portfolio Performance

Comparing the performance of different portfolio allocations requires statistical testing, especially to determine if observed gains are statistically significant.

**[[Content/Key Concepts/Sharpe Ratio Testing|Testing the Sharpe Ratio]]:**

To test if the [[Content/Key Concepts/Sharpe Ratio|Sharpe Ratios]] of two strategies ($i$ and $j$) are equal ($H_0: SR_i = SR_j$), we can test if the difference in Sharpe Ratios ($\Delta = SR_i - SR_j$) is zero. Using sample estimates ($\hat{SR}_i, \hat{SR}_j$), the estimated difference is $\hat{\Delta} = \hat{SR}_i - \hat{SR}_j$.

Under mild conditions, $\sqrt{T}(\hat{\Delta} - \Delta)$ is asymptotically normal. The standard error of $\hat{\Delta}$ can be estimated using the Delta method and a consistent estimator for the asymptotic covariance matrix ($\Psi$) of the sample moments.

**[[Content/Key Concepts/HAC Inference|HAC Inference]] and [[Content/Key Concepts/Bootstrap Methods|Bootstrap Methods]]:**

Heteroskedasticity and Autocorrelation Robust (HAC) estimators (e.g., Newey-West) or bootstrap methods (especially time-series bootstrap) are used to estimate the standard errors of performance measures like the Sharpe Ratio, accounting for the time-series properties of financial data. Bootstrap methods are often preferred in small samples due to better properties.

**Testing Variances:**

To compare the variances of two strategies ($H_0: \sigma_i^2 = \sigma_j^2$), it's equivalent to testing if the log ratio of variances ($\Delta = \log \sigma_i^2 - \log \sigma_j^2$) is zero. Similar to Sharpe Ratio testing, the Delta method and robust standard errors (HAC or bootstrap) are used.

### Empirical Example Insights

An empirical example comparing minimum-variance and tangency portfolios using industry sorts highlights:

*   The tangency portfolio, while theoretically optimal for mean-variance investors, may perform poorly empirically due to the difficulty in accurately estimating expected returns ($\tilde{\mu}$). The GMV portfolio, which doesn't rely on expected return estimates, often performs better.
*   Imposing a factor structure (like [[Content/Key Concepts/Fama-French Model|Fama-French]]) can potentially improve portfolio performance (e.g., increasing the Sharpe Ratio of the GMV portfolio), but these gains may not always be statistically significant, emphasizing the challenges of empirical portfolio construction and the need for robust testing.

### Key Takeaways

*   The inverse covariance matrix has a useful interpretation through linear regressions and hedging portfolios.
*   Portfolio optimization problems can be framed and solved using regression techniques.
*   Robust statistical methods (HAC, bootstrap) are essential for testing the significance of portfolio performance differences, especially for Sharpe Ratios and variances.

**Recommended Reading:**

*   Ledoit, O., & Wolf, M. (2008). Robust performance hypothesis testing with the Sharpe ratio. Journal of Empirical Finance, 15, 850–859.
*   Ledoit, O., & Wolf, M. (2011). Robust performances hypothesis testing with the variance. Wilmott, 2011, 86–89.

**Next Lecture:** More on implementation issues.