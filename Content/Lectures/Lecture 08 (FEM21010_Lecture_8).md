# Lecture 08 (FEM21010_Lecture_8)

## Large-N Portfolio Allocation

This lecture addresses the challenges of [[Content/Key Concepts/High-Dimensional Portfolio Allocation|portfolio allocation when the number of assets ($N$) is large]], potentially exceeding the time series length ($T$). The focus shifts to the estimation of the covariance matrix ($\Sigma$) and the inverse covariance matrix ($\Sigma^{-1}$).

### High-Dimensionality Challenges

In portfolio management, high dimensionality arises from the number of assets ($N$) and factors ($K$). The primary challenge with large $N$ is:

* **Estimation Uncertainty:** With many parameters to estimate (N means and N(N+1)/2 covariances), estimation error becomes significant.
* **Invertibility:** The sample covariance matrix is not invertible when $N > T$, which is problematic as mean-variance allocations require $\Sigma^{-1}$. Estimates of $\Sigma$ must be invertible and well-conditioned.

### Estimating Large-N Covariance Matrices ($\Sigma$)

Various methods are used to estimate $\Sigma$ in high dimensions, often involving shrinkage or factor structures:

* **[[Content/Key Concepts/Factor Models for Large-N Covariance Matrices|Factor Models]]:** Impose structure on $\Sigma$ using a factor model $\Sigma = B\Sigma_f B' + \Sigma_{\epsilon}$. Can restrict $\Sigma_{\epsilon}$ (e.g., diagonal) or use shrinkage (e.g., Ledoit-Wolf shrinkage towards a factor model implied covariance matrix).
* **[[Content/Key Concepts/Eigenvalue Shrinkage|Shrinkage on Eigenvalues]]:** Decompose $\Sigma$ spectrally ($\Sigma = \Lambda D \Lambda'$) and shrink the sample eigenvalues towards a target (e.g., their grand mean). This can be done without imposing a specific financial factor model structure and can yield well-conditioned estimators.
* **[[Content/Key Concepts/POET|Approximate Factor Models and Sparsity (POET)]]:** Assumes returns follow an approximate factor structure with unobserved factors and a sparse error covariance matrix ($\Sigma_{\epsilon}$). Uses eigenvalue decomposition of the sample covariance matrix to separate the common factor component from the idiosyncratic component. Sparsity (many zero elements) is then imposed on the estimated idiosyncratic covariance matrix through thresholding.

### Estimating Large-N Inverse Covariance Matrices ($\Sigma^{-1}$) Directly

Since portfolio allocations directly depend on $\Sigma^{-1}$, estimating it directly can circumvent the issues with inverting a large $\Sigma$. $\Sigma^{-1}$ can be interpreted as summarizing hedging portfolios.

* **[[Content/Key Concepts/Nodewise Regression|Regression Representation (Nodewise Regression)]]:** Recall from Lecture 3 that $\Sigma^{-1}$ elements relate to coefficients and residual variances from regressing each asset on all others ($r_i = a + \sum_{j \neq i} \phi_j r_j + \epsilon_i$). For $N > T$, OLS is not defined. Regularized regressions like Lasso (L1 penalty) are used to estimate the coefficients, promoting sparsity in the estimated parameter vectors. The **Nodewise regression approach (Callot et al., 2021)** performs this Lasso regression for each asset to estimate the rows/columns of $\Sigma^{-1}$. This implies that assets are optimally hedged by a sparse portfolio of other assets.
* **[[Content/Key Concepts/Graphical Lasso|Graphical Lasso]]:** Estimates $\Sigma^{-1}$ system-wide by maximizing a regularized Gaussian log-likelihood with an L1 penalty on the off-diagonal elements of $\Sigma^{-1}$. This promotes sparsity in $\Sigma^{-1}$ and guarantees a positive semi-definite estimate. The off-diagonal elements of $\Sigma^{-1}$ represent conditional covariances (or partial correlations), so sparsity in $\Sigma^{-1}$ implies conditional independence between assets given others.

### Reconciling Factor Models and Direct Inverse Estimation

Even with a strict factor structure, $\Sigma^{-1}$ is generally not sparse. The common factor structure induces non-zero partial correlations between assets. However, the inverse of the *residual* covariance matrix ($\Delta^{-1}$) conditional on the factors *can* be sparse. Sparsity methods (Nodewise, Graphical Lasso) can be applied to the residuals after accounting for the factor structure.

### Key Takeaways

* Portfolio allocation with large $N$ shifts the focus to accurately estimating covariance and inverse covariance matrices.
* When $N > T$, the sample covariance matrix is not invertible, necessitating regularization techniques.
* Methods for estimating $\Sigma$ include factor models, eigenvalue shrinkage, and POET, which use sparsity assumptions on the covariance or residual covariance matrix.
* Methods for estimating $\Sigma^{-1}$ directly include Nodewise regression and Graphical Lasso, which impose sparsity assumptions on the inverse covariance matrix.
* Sparsity in $\Sigma^{-1}$ implies that assets can be optimally hedged by a small number of other assets.
* Factor models and sparsity in the inverse covariance matrix can be reconciled by considering sparsity in the inverse of the residual covariance matrix conditional on the factors.

**Recommended Reading:**

* Callot, L., Caner, M., Onder, A. O., & Ulasan, E. (2021). A nodewise regression approach to estimating large portfolios. Journal of Business & Economic Statistics, 39, 520–531.
* Goto, S., & Xu, Y. (2015). Improving mean variance optimization through sparse hedging restrictions. Journal of Financial and Quantitative Analysis, 50, 1415–1441. (Sections I, II and III)

**Next Lecture:** Long-term portfolio allocation.