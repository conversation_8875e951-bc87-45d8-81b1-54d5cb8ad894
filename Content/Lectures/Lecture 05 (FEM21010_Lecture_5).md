# Lecture 05 (FEM21010_Lecture_5)

## Estimation Risk and Shrinkage Estimation

This lecture delves into the problems of [[Content/Key Concepts/Parameter Uncertainty|parameter uncertainty]] and estimation risk in portfolio optimization and introduces [[Content/Key Concepts/Shrinkage Estimation|shrinkage estimation]] as a method to mitigate these issues.

### Parameter Uncertainty

The **[[Content/Key Concepts/Jobson-Korkie Experiment|Jobson-Korkie experiment]]** highlighted that using sample estimates for expected returns and covariance matrices in mean-variance optimization leads to significant estimation risk and poor out-of-sample performance. This is because the optimizer tends to amplify estimation errors, particularly in expected returns.

**[[Content/Key Concepts/Kan-Zhao Framework|Ka<PERSON> and <PERSON> (2007) (KZ)]]** provide an analytical framework to quantify the economic loss due to parameter uncertainty under the assumption of i.i.d. normal excess returns. They analyze three cases:

1. **Uncertain $\mu$, known $\Sigma$:** The economic loss is proportional to $N/T$, where $N$ is the number of assets and $T$ is the time series length. Loss = $$\frac{N}{2\gamma T}$$.
2. **Uncertain $\Sigma$, known $\mu$:** The economic loss is proportional to the squared Sharpe ratio and a factor depending on $N$ and $T$. Loss = $$\frac{1}{2\gamma} \mu'\Sigma^{-1}\mu (1-k)$$, where $k$ is a complex function of $T$ and $N$.
3. **Both $\mu$ and $\Sigma$ uncertain:** The total loss is a combination of the losses from cases 1 and 2. For large $N/T$, uncertainty in $\Sigma$ can contribute significantly to the loss, contrary to the common perception that only expected return estimation is problematic.

### Optimal Portfolio Choice Under Parameter Uncertainty

When parameters are unknown, the standard mean-variance allocation $w = \gamma^{-1} \Sigma^{-1} \mu$ is no longer optimal. Adjustments are needed to account for estimation risk.

**Scaling Risky Holdings:** One approach is to scale the standard plug-in mean-variance portfolio ($w_{plugin} = \gamma^{-1} \hat{\Sigma}^{-1} \hat{\mu}$) by a factor $c$. KZ derive an optimal scaling factor $c^{**}$ that maximizes expected utility under parameter uncertainty. This factor depends on the true Sharpe ratio and $N/T$.

**Portfolio Combinations:** Combining different portfolio rules can also reduce estimation risk. A common approach is to combine the plug-in tangency portfolio and the [[Content/Key Concepts/Global Minimum Variance Portfolio|Global Minimum Variance (GMV) portfolio]]. KZ derive the optimal weights for such a combination, showing that the optimal portfolio is a linear combination of the estimated tangency portfolio and the estimated GMV portfolio.

### Shrinkage Estimation

[[Content/Key Concepts/Shrinkage Estimation|Shrinkage estimation]] aims to reduce estimation error in inputs (like $\mu$ and $\Sigma$) by imposing restrictions or combining the estimate with a target. This relies on the principle that even a biased estimator can have lower Mean Squared Error (MSE) than an unbiased one if the reduction in variance outweighs the increase in bias.

**[[Content/Key Concepts/Bias-Variance Tradeoff|Bias-Variance Decomposition]]:** MSE of an estimator $\hat{\beta}$ for $\beta$ can be decomposed as $$MSE(\hat{\beta}) = Bias(\hat{\beta})^2 + Var(\hat{\beta})$$. Shrinkage increases bias but reduces variance.

**[[Content/Key Concepts/James-Stein Estimator|James-Stein Estimator]]:** For estimating a mean vector $\mu$ in a multivariate normal setting with $N > 2$, the sample mean $\hat{\mu}$ is dominated in terms of MSE by the James-Stein estimator, which shrinks the sample mean towards a target (e.g., a common mean for all assets). The shrinkage intensity depends on $N$, $T$, and the distance between the sample mean and the target.

**Bayesian Interpretation:** Shrinkage estimators have a Bayesian interpretation as the posterior mean when using an informative prior. The prior mean acts as the shrinkage target, and the prior variance determines the shrinkage intensity.

Shrinkage techniques, such as shrinking sample means towards a common mean or a factor model implied mean, have been shown to improve the performance of mean-variance portfolios in simulations (e.g., in the Jobson-Korkie experiment).

### Key Takeaways

* Parameter uncertainty, especially in expected returns and covariance matrices, significantly impacts the performance of mean-variance portfolios.
* Analytical frameworks (like KZ) help understand the sources and magnitude of economic losses due to estimation risk.
* Optimal portfolio choice under uncertainty involves adjusting standard rules, such as scaling risky positions or combining different portfolio strategies.
* Shrinkage estimation is a valuable technique to reduce estimation error in portfolio inputs by trading off bias for variance.
* Shrinkage can be interpreted from both frequentist (James-Stein) and Bayesian perspectives.

**Recommended Reading:**

* Brandt (2010). Portfolio choice problems, Page 301 - 304.
* Kan and Zhao (2007). Portfolio choice problems with parameter uncertainty. Review of Financial Studies, 20(4), 1123-1156. (Sections I, II.A, II.B, II.E until Figure 2, III)

**Next Lecture:** Extending the econometric toolkit, including the 1/N portfolio.