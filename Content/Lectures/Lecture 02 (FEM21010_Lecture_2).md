# Lecture 02 (FEM21010_Lecture_2)

## Markowitz Portfolio Theory

This lecture introduces the [[Content/Key Concepts/Modern Portfolio Theory|Markowitz framework]] for portfolio selection, focusing on mean-variance optimization with and without a risk-free rate, the [[Content/Key Concepts/Efficient Frontier|efficient frontier]], and the combination with financial factor models.

### Key Assumptions of Mean-Variance Portfolio Choice

* Investors face a trade-off between risk and expected returns.
* Portfolio choice is based on measurable risk (variance/standard deviation).
* Optimal choice depends on the first (mean) and second (covariance) moments of returns.

### Mean-Variance Portfolio without a Risk-Free Rate

The problem is to minimize portfolio variance for a given level of expected return, subject to the constraint that portfolio weights sum to 1.

* **Objective:** $$\min_w w'\Sigma w$$
* **Constraints:**
  * $$w'\iota = 1$$ (weights sum to 1)
  * $$w'\mu = \mu_p$$ (achieve target expected return $\mu_p$)

Where:

* $w$ is the vector of portfolio weights.
* $\Sigma$ is the covariance matrix of asset returns.
* $\iota$ is a vector of ones.
* $\mu$ is the vector of expected asset returns.
* $\mu_p$ is the target portfolio expected return.

The solution involves the Lagrangian method and results in portfolio weights that are a linear combination of two component portfolios. This relationship between portfolio expected return and standard deviation forms a hyperbola in the mean-standard deviation diagram, known as the **[[Content/Key Concepts/Efficient Frontier|Efficient Frontier]]** (without a risk-free rate).

### Adding a Risk-Free Asset

Introducing a risk-free asset simplifies the problem. Combinations of the risk-free asset and any risky asset/portfolio lie on a straight line in the mean-standard deviation diagram. The slope of this line is the [[Content/Key Concepts/Sharpe Ratio|Sharpe ratio]].

The problem becomes minimizing portfolio variance for a given expected *excess* return:

* **Objective:** $$\min_w w'\Sigma w$$
* **Constraint:** $$w'\tilde{\mu} = \tilde{\mu}_p$$

Where:

* $\tilde{\mu} = \mu - \iota R_f$ is the vector of expected excess returns.
* $\tilde{\mu}_p = \mu_p - R_f$ is the target portfolio expected excess return.
* $R_f$ is the risk-free rate.

The variance $\sigma_p^2$ is still $w'\Sigma w$, as $R_f$ is risk-free.

The solution leads to the concept of the **[[Content/Key Concepts/Tangency Portfolio|Tangency Portfolio]]** ($w_{TAN}$), which is the risky portfolio with the highest Sharpe ratio. All efficient portfolios are combinations of the risk-free asset and this single tangency portfolio. They lie on a straight line (the Capital Allocation Line) originating from the risk-free rate and passing through the tangency portfolio.

* **Tangency Portfolio Weights:** $$w_{TAN} = \frac{\Sigma^{-1} \tilde{\mu}}{\iota' \Sigma^{-1} \tilde{\mu}}$$

### Global Minimum Variance Portfolio (GMV)

This portfolio minimizes variance without any constraint on expected return (other than weights summing to 1). It is often found to be more empirically successful than mean-variance optimized portfolios due to greater compositional stability.

* **Objective:** $$\min_w w'\Sigma w$$
* **Constraint:** $$w'\iota = 1$$

* **[[Content/Key Concepts/Global Minimum Variance Portfolio|GMV Portfolio Weights]]:** $$w_{GMV} = \frac{\Sigma^{-1} \iota}{\iota' \Sigma^{-1} \iota}$$

The GMV portfolio weights depend only on the covariance matrix $\Sigma$, not the expected returns $\mu$, which are notoriously difficult to estimate accurately.

### Certainty Equivalence (CE) Perspective

Mean-variance optimization can be viewed through the lens of maximizing the [[Content/Key Concepts/Certainty Equivalent|Certainty Equivalent (CE)]], which is the risk-less return an investor would accept instead of a risky portfolio. Under certain assumptions (e.g., [[Content/Key Concepts/Quadratic Utility|quadratic utility]] or normally distributed returns), maximizing CE is equivalent to mean-variance optimization.

* **CE Approximation:** $$CE \approx \mu_p - \frac{1}{2} \gamma \sigma_p^2$$

Where $\gamma$ is the coefficient of [[Content/Key Concepts/Risk Aversion|risk aversion]]. This shows the trade-off between expected return (positive contribution) and variance (negative contribution, scaled by risk aversion).

### Markowitz Optimization with Factor Models

[[Content/Key Concepts/Factor Models for Portfolio Optimization|Factor models]] (like [[Content/Key Concepts/CAPM|CAPM]] and [[Content/Key Concepts/Fama-French Model|Fama-French]]) can inform Markowitz portfolios by providing structure for estimating expected returns and covariance matrices.

* **CAPM Implication:** If CAPM holds, the tangency portfolio is the market portfolio. Optimal investment is in the risk-free rate and the market portfolio.
* **Multifactor Model Implication:** If a multifactor model holds (and the intercepts 'a' are zero), optimal investment is only in the traded factors and the risk-free rate.
* If the models do not perfectly hold (a $\neq$ 0), investment in individual risky assets is justified, depending on the deviation 'a' and the residual covariance matrix $\Sigma_{\epsilon}$.
* Factor models provide a structured way to estimate $\mu$ and $\Sigma$, potentially leading to better portfolio performance than using sample estimates, especially for large numbers of assets.

### Limitations

Mean-variance analysis relies on assumptions about utility functions or return distributions. In reality, expected utility depends on all moments of the wealth distribution, not just mean and variance. Mean-variance analysis can be seen as a second-order approximation.

### Key Takeaways

* Understand the derivation and properties of [[Content/Key Concepts/Efficient Frontier|mean-variance efficient portfolios]].
* Be able to calculate optimal portfolios using matrix algebra.
* Recognize the relationship between [[Content/Key Concepts/Modern Portfolio Theory|Markowitz portfolios]] and [[Content/Key Concepts/Factor Models for Portfolio Optimization|factor models]].
* Be aware of the empirical challenges and limitations, particularly the difficulty of estimating expected returns.

**Next Lecture:** [[Content/Lectures/Lecture 03 (FEM21010_Lecture_3)|A regression perspective and testing performance differences]].