# Lecture 07 (FEM21010_Lecture_7)

## 1/N, Constrained, and Timing Portfolios

This lecture concludes the discussion on short-term portfolio optimization with a small number of assets, focusing on the performance and theoretical properties of the [[Content/Key Concepts/1-N Portfolio Strategy|1/N portfolio]], [[Content/Key Concepts/Constrained Mean-Variance Allocation|constrained mean-variance allocations]], and timing strategies.

### The 1/N Portfolio Revisited

The 1/N equally weighted portfolio is a simple strategy that allocates $1/N$ of wealth to each of the $N$ assets. Despite its simplicity and lack of reliance on parameter estimation, empirical studies (like DGU) show it is a tough benchmark to beat out-of-sample for many sophisticated strategies.

**Theoretical Analysis (Multivariate Normal Framework):** Using the framework from [[Content/Key Concepts/Kan-Zhao Framework|Ka<PERSON> and <PERSON> (2007)]] with i.i.d. multivariate normal excess returns, the economic loss of the 1/N portfolio relative to the true optimal tangency portfolio can be expressed in terms of the squared Sharpe Ratios of the tangency and 1/N portfolios:

$$L(w^*, w_{ew}) = \frac{1}{2\gamma} (SR_{tan}^2 - SR_{ew}^2)$$

Comparing this loss to the expected loss of the sample-based tangency portfolio reveals that the sample-based plug-in method only outperforms 1/N when the time series length ($T$) is sufficiently large relative to the number of assets ($N$) and the difference in true Sharpe Ratios. This "[[Content/Key Concepts/Critical Sample Size for 1-N|critical sample size]]" can be very large, especially when the true Sharpe Ratios are similar.

**Simulation and Empirical Insights (DGU):** Simulations confirm that a large estimation window ($M$) is required for sample-based mean-variance portfolios to outperform 1/N, and this requirement increases with $N$. Empirically, DGU found that 1/N often has comparable or higher Sharpe Ratios and significantly lower turnover than many complex strategies across various datasets. This is attributed to 1/N avoiding the large estimation errors that plague more complex models.

### Constrained Mean-Variance Allocation

[[Content/Key Concepts/Constrained Mean-Variance Allocation|Constraining mean-variance portfolios]] can improve their empirical performance. A constrained mean-variance portfolio (maximizing utility subject to weights summing to 1) can be shown to be a linear combination of the tangency portfolio (TP) and the global minimum variance (GMV) portfolio:

$$w_{OC} = a w_{TP} + (1-a) w_{GMV}$$

Where the coefficient $a$ depends on the target expected return (e.g., the expected return of the 1/N portfolio) and the expected returns of the TP and GMV portfolios. Fixing the target return to that of the 1/N allocation is a common approach. This constrained portfolio lies on the efficient frontier and can offer improved performance by limiting extreme positions driven by estimation error.

### Timing Portfolios

Timing strategies focus on allocating wealth based on time-varying characteristics of assets, often ignoring covariance structures (implicitly assuming a diagonal covariance matrix). Two examples are:

* **[[Content/Key Concepts/Volatility Timing|Volatility Timing]]:** Weights are inversely proportional to asset variances: $$w_{i,\sigma^2} = \frac{1/\sigma_i^2}{\sum_{j=1}^N 1/\sigma_j^2}$$
* **[[Content/Key Concepts/Risk-Reward Timing|Risk-Reward Timing]]:** Weights are proportional to the ratio of expected return to variance: $$w_{i,\mu/\sigma^2} = \frac{\mu_i/\sigma_i^2}{\sum_{j=1}^N \mu_j/\sigma_j^2}$$

These strategies are simpler to implement (no matrix inversion needed) and can be easily constrained (e.g., no short-selling). A timing parameter $\eta$ can be introduced to control the intensity of timing, with $\eta=0$ resulting in the 1/N portfolio. Empirical results suggest that timing portfolios, particularly volatility timing, can perform well and sometimes outperform 1/N, especially when hedging relationships (covariances) are noisy.

### Summary of Short-Term Portfolio Optimization (Few Assets)

* Optimal portfolios for short-term investments without parameter uncertainty can be derived ([[Content/Key Concepts/Modern Portfolio Theory|Markowitz framework]] - TP, GMV, MV).
* [[Content/Key Concepts/Parameter Uncertainty|Parameter uncertainty]] significantly impacts performance, as shown by simulations ([[Content/Key Concepts/Jobson-Korkie Experiment|Jobson-Korkie]]), theory (KZ), and empirical studies (DGU).
* An econometric toolkit exists to improve performance by addressing estimation risk: [[Content/Key Concepts/Shrinkage Estimation|shrinkage estimation]] ([[Content/Key Concepts/James-Stein Estimator|James-Stein]], Pastor, Ledoit-Wolf), regression representations ([[Content/Key Concepts/Britten-Jones Regression|Britten-Jones]]), [[Content/Key Concepts/Portfolio Constraints|portfolio constraints]] (Jagannathan-Ma), and portfolio combinations.
* The [[Content/Key Concepts/1-N Portfolio Strategy|1/N portfolio]] serves as a crucial benchmark due to its robustness and low turnover.
* [[Content/Key Concepts/Constrained Mean-Variance Allocation|Constrained mean-variance allocations]] and [[Content/Key Concepts/Volatility Timing|timing portfolios]] offer alternative approaches that can perform well empirically by managing estimation risk and simplifying the problem.

**Remaining Topics:** Portfolio allocation with a large number of assets and portfolio allocation for long investment horizons.

**Recommended Reading:**

* DeMiguel, V., Garlappi, L., & Uppal, R. (2007). Optimal versus Naive Diversification: How Inefficient Is the 1/N Portfolio Strategy? Review of Financial Studies, 22, 1915–1953. (Sections 4 and 5)
* Kirby, C., & Ostdiek, B. (2012). It’s all in the timing: simple active portfolio strategies that outperform naive diversification. Journal of Financial and Quantitative Analysis, 47, 437–467. (Sections I and II)