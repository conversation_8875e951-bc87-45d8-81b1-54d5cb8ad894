# Lecture 04 (FEM21010_Lecture_4)

## Implementing <PERSON><PERSON>itz Portfolios

This lecture discusses practical approaches to implementing portfolio optimization, including frequentist and Bayesian methods, decision theory, and highlights the empirical problems associated with naive mean-variance analysis.

### Objective Functions

While mean-variance preferences (maximizing $$\mu_p - \frac{\gamma}{2} \sigma_p^2$$) are often considered, a more general approach is to maximize expected utility over terminal wealth $W_{t+K}$:

$$\max_w E_t[U(W_{t+K})]$$

For a single period ($K=1$) and unconditional expectation, this is $$\max_w \int u(w'R_{t+1}) p(R_{t+1}|\theta) dR_{t+1}$$, where $\theta = (\mu, \Sigma)$ are the unknown parameters of the return distribution.

### Portfolio Implementation Approaches

Two main approaches exist for dealing with unknown parameters:

1. **[[Content/Key Concepts/Plug-in Approach|Plug-in Approach]]:** Estimate the parameters ($\theta^*$) and plug them into the optimization problem, ignoring parameter uncertainty:
   $$\max_w \int u(w'R_{t+1}) p(R_{t+1}|\theta^*) dR_{t+1}$$
   * **Frequentist:** Uses point estimates like sample mean ($\hat{\mu}$) and sample covariance matrix ($\hat{\Sigma}$). Uncertainty is assessed through the sampling distribution of the estimator.
   * **Bayesian:** Uses moments of the posterior distribution of the parameters (e.g., $E(\mu|Y), E(\Sigma|Y)$). Uncertainty is assessed through the posterior distribution of the portfolio weights.

2. **[[Content/Key Concepts/Decision-Theoretic Approach|Decision-Theoretic Approach]]:** Account for [[Content/Key Concepts/Parameter Uncertainty|parameter uncertainty]] by integrating over the posterior distribution of the parameters. This uses the predictive distribution of returns $$p(R_{t+1}|Y_T) = \int p(R_{t+1}|\theta) p(\theta|Y_T) d\theta$$:
   $$\max_w \int u(w'R_{t+1}) p(R_{t+1}|Y_T) dR_{t+1}$$
   This approach is generally more robust as it considers the full distribution of possible parameter values.

### Uncertainty in Plug-in Estimates

The accuracy of plug-in estimates for portfolio weights depends on the number of assets ($N$) and the time series length ($T$). Precision decreases with $N$ (more parameters to estimate) and increases with $T$. However, time-varying moments often necessitate using small rolling windows for $T$, limiting precision.

Standard errors for plug-in portfolio weights can be derived using asymptotic analysis and the Delta method, or through Bayesian posterior standard deviations.

### Regression Representation for Inference (Britten-Jones, 1999)

The [[Content/Key Concepts/Britten-Jones Regression|Britten-Jones (1999) regression]] provides a way to perform standard OLS inference on [[Content/Key Concepts/Tangency Portfolio|tangency portfolio]] weights, even with many assets. By regressing a vector of ones on excess returns ($$1 = b R_{t+1} + u_{t+1}$$), the OLS estimate $\hat{b}$ is proportional to the tangency portfolio weights ($$w_{TAN} = \frac{\hat{b}}{\iota' \hat{b}}$$). This allows for testing hypotheses about individual or multiple portfolio weights using standard t-tests or F-tests.

### Economic Losses and Error Maximization

Naive implementation of mean-variance portfolios using plug-in estimates often performs poorly in practice due to significant estimation error. The **[[Content/Key Concepts/Jobson-Korkie Experiment|Jobson-Korkie experiment (1980)]]** demonstrated that sample mean-variance frontiers are highly volatile and inferior to the true optimal frontier, and increasing sample size doesn't substantially solve this. [[Content/Key Concepts/Portfolio Constraints|Portfolio constraints]] can help but don't eliminate the instability.

**Michaud (1989)** described mean-variance optimizers as "statistical [[Content/Key Concepts/Error Maximization|error maximizers]]." Because inputs (mean and covariance) are estimated with error, the optimizer tends to assign large weights to assets with positive estimation errors in expected returns and negative estimation errors in variance, effectively optimizing noise rather than true parameters.

### Key Takeaways

* Portfolio implementation can follow plug-in (frequentist or Bayesian) or decision-theoretic approaches.
* The decision-theoretic approach accounts for parameter uncertainty and is generally more robust, although differences may be small for short horizons.
* Naive plug-in mean-variance optimization suffers significantly from estimation error, leading to unstable and suboptimal portfolios (Jobson-Korkie experiment).
* Mean-variance optimizers can act as "statistical error maximizers" by amplifying estimation errors in inputs.
* The Britten-Jones regression offers a frequentist method for inference on tangency portfolio weights.

**Recommended Reading:**

* Britten-Jones (1999). The sampling error in estimates of mean-variance efficient portfolio weights. The Journal of Finance. Vol 54, No 2.
* Jobson and Korkie (1980). Estimation for Markowitz Efficient Portfolios. Journal of the American Statistical Association, Vol. 75, No. 371, pp. 544-554.
* Brandt (2010). Portfolio choice problems, Page 291 - 301 and Page 307 - 312.

**Next Lecture:** More on implementation, focusing on tackling empirical issues.