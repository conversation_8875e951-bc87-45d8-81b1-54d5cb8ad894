# Lecture 09 (FEM21010_Lecture_9)

## Long-Term Portfolio Allocation I

This lecture shifts the focus from short-term, large-$N$ portfolio allocation to long-term allocation, particularly the [[Content/Key Concepts/Strategic Asset Allocation|strategic asset allocation]] decision between asset classes. It introduces a framework for analyzing [[Content/Key Concepts/Long-Term Investment Strategies|long-term investments]] and examines different strategies in an i.i.d. (independent and identically distributed) world.

### Strategic Asset Allocation

Strategic asset allocation is the decision of how to allocate wealth across broad asset classes (e.g., stocks, bonds). This decision is considered the most important determinant of a portfolio's risk-expected return profile. While the theory can apply to many assets, practical implementation often limits the focus to a smaller number of asset classes.

### Conditional Moments

Long-term allocation often considers changes in investment opportunities over time, meaning expected returns and variances can be time-varying. This is captured using [[Content/Key Concepts/Conditional Moments|conditional moments]], such as conditional mean $E_t(R_{t+1})$ and conditional covariance matrix $\Sigma_t = V_t(R_{t+1})$, where the expectation and variance are conditional on information available at time $t$.

### Mean-Variance Analysis in a Long-Term Context

The mean-variance problem can be extended to a long-term setting, considering a risk-free asset or a risky benchmark asset. The optimal allocation $\alpha_t$ (vector of weights in risky assets) at time $t$ is derived by maximizing expected utility, which in a mean-variance context involves the conditional expected excess returns and conditional covariance matrix:

* **With Risk-Free Asset:** $$\alpha_t = \frac{1}{k} \Sigma_t^{-1} E_t(R_{t+1} - \iota R_{f,t+1})$$
* **With Risky Benchmark Asset:** $$\alpha_t = \frac{1}{k} \Sigma_t^{-1} E_t(R_{t+1} - \iota R_{0,t+1}) - \Sigma_t^{-1} \sigma_{0,t}$$

Where $k$ is related to risk aversion, $R_{f,t+1}$ is the risk-free rate, $R_{0,t+1}$ is the benchmark return, and $\sigma_{0,t}$ is the vector of conditional covariances between the benchmark and risky assets. The second case shows the allocation is a combination of a mean-variance term and a term related to the minimum variance portfolio that approximates the risk-free asset.

### Utility Theory for Long-Term Allocation

A broader utility perspective is needed for long-term allocation, maximizing expected utility over terminal wealth $W_{t+K}$. Key concepts include:

* **Absolute Risk Aversion (ARA):** $$-U''(W)/U'(W)$$
* **Relative Risk Aversion (RRA):** $$-WU''(W)/U'(W)$$

Desirable properties for a utility function in finance include non-increasing ARA and approximately constant RRA. The **[[Content/Key Concepts/Power Utility|Power Utility function]]** $$U(W) = \frac{W^{1-\gamma}}{1-\gamma}$$ (with $\gamma$ as the relative risk aversion parameter) satisfies these properties (ARA = $\gamma/W$, RRA = $\gamma$).

Combining Power Utility with **lognormal returns** (where $\log(1+R)$ is normally distributed) provides a tractable framework for long-term analysis, as compounded lognormal returns are also lognormal. However, an approximation is needed for portfolio returns, as the sum of lognormal variables is not lognormal.

### Strategies in an i.i.d. World

An i.i.d. world assumes returns are independent and identically distributed over time, meaning conditional moments are constant ($E_t(R_{t+1}) = E(R)$, $\Sigma_t = \Sigma$). In this simplified setting, several long-term strategies yield the same optimal allocation as the myopic (one-period ahead) investor:

* **Myopic Strategy:** Optimizes for a single period ahead. The optimal allocation in the lognormal-power utility framework is $$\alpha_t = \frac{1}{\gamma} \Sigma_t^{-1} (E_t(r_{t+1} - r_{f,t+1}\iota) + \frac{1}{2} \sigma_t^2)$$, where lowercase $r$ denotes log returns and $\sigma_t^2$ is the vector of conditional variances. This is similar to the mean-variance solution with an additional term related to Jensen's inequality.
* **Buy-and-Hold:** Invests once at the beginning and holds the portfolio until the end of the horizon. In an i.i.d. world, the optimal initial allocation is the same as the myopic solution.
* **Constant Proportion:** Maintains a fixed proportion of wealth in risky assets over time by rebalancing. In an i.i.d. world, the optimal constant proportion is the same as the myopic solution.
* **[[Content/Key Concepts/Dynamic Portfolio Choice|Dynamic Allocation]]:** Allows the investor to change allocations freely in each period. In an i.i.d. world, the optimal dynamic strategy is to maintain the same constant allocation as the myopic solution. This can be shown using backward recursion or by arguing that a constant allocation minimizes variance for a given expected return in an i.i.d. setting.

**Conclusion for i.i.d. World:** If returns are i.i.d., long-term portfolio allocations are the same as short-term (myopic) allocations.

### Fallacies of Long-Term Portfolio Choice

* **Fallacy 1: [[Content/Key Concepts/Time Diversification Fallacy|Time Diversification]]:** The idea that risk (volatility) decreases with the investment horizon. While total variance increases linearly with horizon in an i.i.d. world ($K\sigma^2$), expected return also increases linearly ($K\mu$). Sharpe Ratios, which scale mean by standard deviation, increase with $\sqrt{K}$, but Sharpe Ratios cannot be compared across horizons. Optimal investment depends on mean divided by variance, not Sharpe Ratio.
* **Fallacy 2: [[Content/Key Concepts/Expected Log Return Maximization|Maximizing Expected Log Return]]:** The belief that maximizing expected log return is optimal for long horizons. While this portfolio has the highest probability of outperforming any other over long horizons, it ignores risk aversion and is only optimal for log utility investors ($\gamma=1$). Risk-averse investors with $\gamma > 1$ will prefer portfolios with lower expected log returns but also lower risk.

### Limitations of the Power Utility Framework

The Power Utility framework has limitations, such as linking risk aversion and the elasticity of intertemporal substitution (willingness to smooth consumption). More advanced utility functions like Epstein-Zin recursive utility are needed to separate these preferences.

**Recommended Reading:**

* Campbell and Viceira (2002). Strategic asset allocation: Portfolio Choice for Long-term investors. Chapter 2, Pages 9 to 29.

**Next Lecture:** How short-term and long-term portfolios can diverge when returns are not i.i.d.