# Lecture 10 (FEM21010_Lecture_10)

## Long-Term Portfolio Allocation II

This lecture continues the discussion on long-term portfolio allocation, specifically focusing on [[Content/Key Concepts/Dynamic Portfolio Choice|dynamic strategies]] when returns are not i.i.d. It introduces the [[Content/Key Concepts/Bellman Equation|Bellman equation]], numerical solution methods, and insights from the perspective of the average investor.

### Dynamic Long-Term Portfolio Choice (Non-i.i.d. Returns)

When returns are not i.i.d., investment opportunities (conditional expected returns and covariance matrices) can change over time, influenced by state variables ($z_t$). The optimal portfolio weights at time $t$ depend on the entire path of future weights until the investment horizon.

This dynamic optimization problem is typically formulated using the **<PERSON>man equation**, which expresses the value function $V(\tau, W_t, z_t)$ (maximum expected utility of wealth at horizon $t+\tau$, given current wealth $W_t$ and state $z_t$) recursively:

$$V(\tau, W_t, z_t) = \max_{x_t} E_t[V(\tau-1, W_t(x_t' r_{t+1} + R_{f,t}), z_{t+1})]$$

Where $x_t$ are the portfolio weights chosen at time $t$, $r_{t+1}$ are excess returns, and $R_{f,t}$ is the risk-free rate. The expectation is taken over the joint distribution of $r_{t+1}$ and $z_{t+1}$ conditional on $z_t$.

For [[Content/Key Concepts/Power Utility|Power Utility]] functions, the value function can be separated into a wealth component and a component depending on the horizon and state, simplifying the Bellman equation. However, solving the Bellman equation and its first-order conditions generally requires numerical methods.

### Myopic vs. Long-Term Portfolio Choice (Hedging Demand)

The dynamic (long-term) portfolio choice differs from the myopic (one-period ahead) choice when investment opportunities are predictable (i.e., when state variables $z_t$ exist and influence future returns) and when returns and state variables are correlated. The difference between the dynamic and myopic allocations is called **[[Content/Key Concepts/Hedging Demand|hedging demand]]**.

In continuous time, the optimal portfolio $x_t^*$ can be decomposed into two components:

$$x_t^* = \frac{1}{\gamma} \Sigma_t^{-1} \mu_{p,t} + \text{Hedge Term}$$

* **Myopic Component:** $\frac{1}{\gamma} \Sigma_t^{-1} \mu_{p,t}$ (standard mean-variance allocation, where $\mu_{p,t}$ is the conditional expected excess return vector). This component invests to exploit current expected returns.
* **Hedge Term:** This component arises when investment opportunities are time-varying and correlated with asset returns. It represents the demand for assets that hedge against adverse changes in future investment opportunities (state variables). The hedge term depends on the correlation between asset return innovations and state variable innovations, and the investor's sensitivity to these state variables.

The optimal allocation is myopic (hedge term is zero) if:

1. There are no state variables ($z_t$).
2. State variables are unhedgeable (innovations in returns and state variables are uncorrelated).
3. The investor has log utility ($\gamma=1$).

### Numerical Methods for Dynamic Portfolio Choice

Solving the dynamic optimization problem numerically often involves:

1. **[[Content/Key Concepts/Backward Recursion|Backward Recursion]]:** Starting from the terminal period and working backward to the present. This requires approximating the value function at each step for a range of possible future states.
2. **Simulation:** Generating multiple sample paths of returns and state variables based on an econometric model (e.g., a [[Content/Key Concepts/VAR Framework|VAR model]] for returns and state variables).
3. **[[Content/Key Concepts/Across-Path Regressions|Across-Path Regressions]]:** Using simulated data to approximate conditional expectations. By regressing a function of future outcomes (e.g., future utility) on the state variables at a given time, the fitted values from the regression provide an estimate of the conditional expectation.

This process involves iterating through a grid of possible portfolio weights at each time step and using across-path regressions to find the weight that maximizes the approximated conditional expected future utility.

### Continuous Time Results and the Average Investor

Continuous-time models (like [[Content/Key Concepts/Merton Framework|Merton's framework]]) provide analytical insights into dynamic portfolio choice. The optimal portfolio in continuous time also consists of a myopic component and a hedging demand component. The hedging demand is related to the projection of state variable innovations onto asset return innovations and the investor's aversion to changes in state variables.

Considering the **[[Content/Key Concepts/Average Investor|average investor]]** in equilibrium can provide further insights. The market portfolio is the wealth-weighted average of all investors' portfolios. In equilibrium, the market portfolio's expected return is related to the average investor's risk aversion and aversion to state variables. This implies that if you are the average investor (with average risk aversion and state aversion), your optimal portfolio is the market portfolio.

### Key Takeaways

* Dynamic long-term portfolio choice is complex when investment opportunities are time-varying (non-i.i.d. returns).
* The Bellman equation provides a framework for dynamic optimization, but numerical methods are typically required for solutions.
* The difference between dynamic and myopic allocations is hedging demand, which arises from the desire to hedge against changes in future investment opportunities.
* Numerical techniques like backward recursion, simulation, and across-path regressions are used to solve dynamic portfolio problems.
* Continuous-time models offer analytical insights, confirming the myopic + hedging demand structure.
* The average investor holds the market portfolio, providing a benchmark for individual investors.

**Recommended Reading:**

* Brandt (2010). Portfolio choice problems. Pages 274 - 286.
* van Binsbergen, J., & Brandt, M. (2007). Solving dynamic portfolio choice problems by recursing on optimized portfolio weights or on the value function. Computational Economics, 29.

**Next Lecture:** More on implementation and the effects of parameter uncertainty in the long-term framework.