# Lecture 06 (FEM21010_Lecture_6)

## Portfolio Constraints and the 1/N Strategy

This lecture continues the discussion on addressing estimation risk in portfolio optimization, focusing on combining shrinkage with factor models, imposing portfolio constraints, and evaluating strategies against the simple 1/N equally weighted portfolio.

### Factor Models and Shrinkage Revisited

[[Content/Key Concepts/Factor Models for Portfolio Optimization|Factor models]] can improve estimates of expected returns ($\mu$) and covariance matrices ($\Sigma$) by imposing structure. For a K-factor model $R_{i,t} = \alpha_i + B_i f_t + \epsilon_{i,t}$, the implied moments are $\mu = \alpha + B\mu_f$ and $\Sigma = B\Sigma_f B' + \Sigma_{\epsilon}$.

* **Improving $\mu$ estimates:** Can set $\alpha = 0$ if $\alpha_i$'s are hard to estimate.
* **Improving $\Sigma$ estimates:** Restrict $\Sigma_{\epsilon}$ (e.g., to be diagonal) to reduce the number of parameters.

[[Content/Key Concepts/Shrinkage Estimation|Shrinkage]] can be applied to combine sample estimates with factor model implied estimates (e.g., shrinking the sample covariance matrix towards a factor model implied covariance matrix, as in Led<PERSON> and Wolf (2003)). The optimal shrinkage intensity balances the bias introduced by the model against the reduction in estimation variance.

### Portfolio Constraints

Imposing [[Content/Key Concepts/Portfolio Constraints|constraints on portfolio weights]], such as limiting shortselling ($w_i \ge 0$) or restricting individual asset concentration ($w_i \le \tilde{w}$), can improve empirical performance, particularly in minimum-variance portfolios (Jagannathan and Ma, 2003).

While constraints might seem counter-intuitive (potentially excluding the true optimal portfolio), they can improve the bias-variance trade-off of the estimated portfolio weights. Constraints can increase bias but lead to a greater reduction in the variance of the estimated weights, resulting in better out-of-sample performance.

**[[Content/Key Concepts/Shrinkage Interpretation of Constraints|Shrinkage Interpretation of Constraints]]:** Portfolio constraints can be interpreted as implicitly shrinking the sample covariance matrix towards a modified matrix. Binding constraints alter the elements of the covariance matrix in a way that resembles shrinkage, effectively reducing the impact of large positive or negative estimated covariances associated with extreme weights.

### The 1/N Portfolio

The [[Content/Key Concepts/1-N Portfolio Strategy|1/N strategy]], which allocates equal weight ($1/N$) to each of the $N$ available assets, serves as a simple benchmark. It requires no parameter estimation or complex optimization.

**DeMiguel, Garlappi, and Uppal (2009) (DGU)** extensively compare various portfolio strategies, including sample-based Markowitz, Bayesian approaches, shrinkage estimators, and constrained portfolios, against the 1/N rule using out-of-sample data from different asset classes.

**Why 1/N is a Tough Competitor:**

* Avoids estimation error entirely.
* Simple to implement.
* Often exhibits low turnover (less trading, lower transaction costs).

DGU's findings show that despite ignoring all data information, the 1/N strategy is often hard to beat in terms of out-of-sample Sharpe ratios and frequently has lower turnover compared to more sophisticated methods. This highlights that the "loss from estimation error" in complex models can outweigh the "gain from optimal diversification" they theoretically offer.

However, 1/N ignores potentially valuable information about expected returns and risk that could be exploited for better performance if estimation risk is managed effectively.

### Key Takeaways

* Combining factor models with shrinkage techniques can improve the estimation of portfolio inputs.
* Portfolio constraints, such as limits on shortselling or concentration, can enhance empirical performance by improving the bias-variance trade-off of estimated weights and have a shrinkage interpretation.
* The simple 1/N equally weighted portfolio is a strong benchmark due to its robustness against estimation error and low turnover.
* Empirical studies (like DGU) show that many sophisticated portfolio strategies struggle to consistently outperform 1/N out-of-sample, emphasizing the practical challenges of portfolio optimization.
* Minimum variance portfolios, especially when constrained, tend to perform relatively well compared to other strategies.

**Recommended Reading:**

* DeMiguel, V., Garlappi, L., & Uppal, R. (2007). Optimal versus Naive Diversification: How Inefficient Is the 1/N Portfolio Strategy? Review of Financial Studies, 22, 1915–1953.
* Jagannathan, R., & Ma, T. (2003). Risk reduction in large portfolios: Why imposing constraints helps. Journal of Finance, 58(4), 1651-1683.

**Next Lecture:** Closing short-term portfolio optimization with few assets, analyzing 1/N in the multivariate normal framework, mean-constrained mean-variance allocations, and timing portfolios.