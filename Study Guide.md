# Portfolio Management Resit Exam Study Guide

This guide provides a focused 1-week study plan for your portfolio management resit exam, emphasizing practical applications and active learning through practice.

## Exam Focus Areas
See the [[Course Summary#Exam Focus Areas]] for a comprehensive overview of the most important topics for the exam.

## Essential Formulas
For a quick reference of all exam-relevant formulas, check:
- Portfolio construction: ![[Key Formulas#^portfolio-construction]]
- Return calculations: ![[Key Formulas#^return-formulas]]
- Risk measures: ![[Key Formulas#^risk-measures]]
- Performance metrics: ![[Key Formulas#^performance-metrics]]
- Shrinkage estimation: ![[Key Formulas#^shrinkage]]
- Long-term investment: ![[Key Formulas#^long-term]]

## 1-Week Intensive Study Plan

### Day 1: Foundation Review
- **Morning (2 hours)**: Review [[Content/Key Concepts/Modern Portfolio Theory|Modern Portfolio Theory]] and [[Content/Key Concepts/Efficient Frontier|Efficient Frontier]]
- **Afternoon (3 hours)**: Practice portfolio construction problems
  - Calculate optimal weights for tangency portfolios
  - Construct minimum variance portfolios
  - Analyze portfolio constraints effects
- **Evening (1 hour)**: Create formula flashcards for quick recall

### Day 2: Parameter Uncertainty & Estimation
- **Morning (2 hours)**: Study [[Content/Key Concepts/Parameter Uncertainty|Parameter Uncertainty]] and [[Content/Key Concepts/Jobson-Korkie Experiment|Jobson-Korkie findings]]
- **Afternoon (3 hours)**: Practice with [[Content/Key Concepts/Britten-Jones Regression|regression approaches]] to portfolio weights
  - Calculate standard errors for portfolio weights
  - Implement shrinkage estimators
- **Evening (1 hour)**: Review and summarize key insights

### Day 3: Robust Portfolio Construction
- **Morning (2 hours)**: Study [[Content/Key Concepts/Shrinkage Estimation|Shrinkage Estimation]] and [[Content/Key Concepts/1-N Portfolio Strategy|1/N strategy]]
- **Afternoon (3 hours)**: Practice implementing robust portfolios
  - Compare performance of different strategies
  - Analyze the impact of constraints
- **Evening (1 hour)**: Create concept maps linking related topics

### Day 4: Large-N Portfolios & Factor Models
- **Morning (2 hours)**: Review [[Content/Key Concepts/Factor Models for Portfolio Optimization|Factor Models]] and [[Content/Key Concepts/High-Dimensional Portfolio Allocation|Large-N techniques]]
- **Afternoon (3 hours)**: Practice with factor-based covariance estimation
  - Implement principal component analysis
  - Construct factor-based portfolios
- **Evening (1 hour)**: Summarize key techniques and their applications

### Day 5: Long-Term Investment Strategies
- **Morning (2 hours)**: Study [[Content/Key Concepts/Long-Term Investment Strategies|Long-Term Investment]] and [[Content/Key Concepts/Hedging Demand|Hedging Demand]]
- **Afternoon (3 hours)**: Practice with multi-period portfolio problems
  - Calculate myopic vs. hedging demands
  - Analyze the impact of predictability
- **Evening (1 hour)**: Create summary sheets for quick reference

### Day 6: Practice Exam & Targeted Review
- **Morning (3 hours)**: Complete a full practice exam (use [[Content/Exams/Exam (FEM21010_Exam1920a)|past exam]] as reference)
- **Afternoon (3 hours)**: Review and correct exam answers
  - Focus on areas of weakness
  - Practice similar problems
- **Evening (1 hour)**: Update study materials based on identified gaps

### Day 7: Final Integration & Review
- **Morning (2 hours)**: Review all key concepts and formulas
  - Focus on connections between topics
  - Review common exam question types
- **Afternoon (2 hours)**: Practice quick calculations and concept application
  - Time yourself on formula applications
  - Practice explaining concepts concisely
- **Evening (1 hour)**: Light review of most challenging topics

## Active Learning Strategies

### Practice-Based Learning
1. **Solve Before Reading**: Attempt to solve problems before reviewing solutions
2. **Implement Formulas**: Calculate portfolio weights, risk measures, and performance metrics with real data
3. **Compare Strategies**: Implement and compare different portfolio construction approaches
4. **Explain Out Loud**: Verbalize your understanding of concepts and solution approaches

### Effective Review Techniques
1. **Concept Maps**: Create visual connections between related topics
2. **Formula Application Cards**: For each formula, create cards with:
   - The formula itself
   - When to use it
   - Step-by-step application process
   - Common pitfalls
3. **Self-Testing**: Generate and solve your own exam-style questions
4. **Teach-Back Method**: Explain complex concepts as if teaching someone else

## Common Exam Question Types
See [[Course Summary#Common Exam Question Types]] for detailed information on what to expect in the exam.

## Key Resources
- [[Content/Exams/Exam (FEM21010_Exam1920a)|Past Exam]] - Use for practice and to understand question formats
- [[Key Formulas]] - Comprehensive formula reference with applications
- [[Hubs/Key Concepts Hub|Key Concepts Hub]] - Detailed explanations of theoretical frameworks