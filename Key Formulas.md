# Detailed Formula Sheet - Portfolio Management (FEM21010)

This document provides a comprehensive compilation of key formulas and concepts for the FEM21010 Portfolio Management course, with context and explanations for deeper understanding.

## I. Foundations of Portfolio Theory & Mean-Variance Optimization

This section covers the basic building blocks of portfolio construction as introduced by <PERSON><PERSON><PERSON>.

### 1. Basic Portfolio Calculations

**Expected Portfolio Return ($E[R_p]$ or $\mu_p$):**
The weighted average of the expected returns of the individual assets in the portfolio.

$E[R_p] = \mathbf{w}'\boldsymbol{\mu} = \sum_{i=1}^{N} w_i \mu_i$

- $\mathbf{w}$: Vector of portfolio weights ($N \times 1$)
- $\boldsymbol{\mu}$: Vector of expected asset returns ($N \times 1$)
- $w_i$: Weight of asset $i$
- $\mu_i$: Expected return of asset $i$
- $N$: Number of assets

**Portfolio Variance ($\sigma_p^2$):**
A measure of the dispersion of portfolio returns, accounting for the variances of individual assets and their covariances.

$\sigma_p^2 = \mathbf{w}'\Sigma\mathbf{w} = \sum_{i=1}^{N} \sum_{j=1}^{N} w_i w_j \sigma_{ij} = \sum_{i=1}^{N} w_i^2 \sigma_i^2 + \sum_{i=1}^{N} \sum_{j \neq i}^{N} w_i w_j \sigma_{ij}$

- $\Sigma$: Covariance matrix of asset returns ($N \times N$)
- $\sigma_{ij}$: Covariance between asset $i$ and asset $j$
- $\sigma_i^2$: Variance of asset $i$

**Portfolio Standard Deviation ($\sigma_p$):**
The square root of the portfolio variance, representing the total risk of the portfolio.

$\sigma_p = \sqrt{\mathbf{w}'\Sigma\mathbf{w}}$

### 2. Mean-Variance Efficient Portfolios (No Risk-Free Asset)

These portfolios offer the highest expected return for a given level of risk or the lowest risk for a given expected return.

**General Form of Mean-Variance Efficient Portfolio Weights ($\mathbf{w}_{MV}$):**
The weights of any portfolio on the efficient frontier (excluding the GMV if it's the unique minimum) can be expressed as a linear combination based on the target expected return $\mu_p$.

$\mathbf{w}_{MV} = \Lambda_0 + \Lambda_1 \mu_p$

Where:
$\Lambda_0 = \frac{1}{D}[B\Sigma^{-1}\iota - A\Sigma^{-1}\boldsymbol{\mu}]$
$\Lambda_1 = \frac{1}{D}[C\Sigma^{-1}\boldsymbol{\mu} - A\Sigma^{-1}\iota]$

And the scalars are:
$A = \iota'\Sigma^{-1}\boldsymbol{\mu}$
$B = \boldsymbol{\mu}'\Sigma^{-1}\boldsymbol{\mu}$
$C = \iota'\Sigma^{-1}\iota$
$D = BC - A^2$

- $\iota$: Vector of ones ($N \times 1$)

**[[Content/Key Concepts/Global Minimum Variance Portfolio|Global Minimum Variance Portfolio (GMV)]] ($\mathbf{w}_{GMV}$):**
The portfolio on the efficient frontier with the absolute lowest variance. Its weights depend only on the covariance matrix.

$\mathbf{w}_{GMV} = \frac{\Sigma^{-1}\iota}{\iota'\Sigma^{-1}\iota}$

This portfolio corresponds to a target return $\mu_p = A/C$.

### 3. Mean-Variance Efficient Portfolios (With a Risk-Free Asset)

The introduction of a risk-free asset (return $R_f$) simplifies the efficient frontier to a straight line, the Capital Allocation Line (CAL).

**[[Content/Key Concepts/Tangency Portfolio|Tangency Portfolio (TP)]] ($\mathbf{w}_{TAN}$):**
The unique portfolio of risky assets on the efficient frontier that, when combined with the risk-free asset, offers the highest [[Content/Key Concepts/Sharpe Ratio|Sharpe Ratio]].

$\mathbf{w}_{TAN} = \frac{\Sigma^{-1}(\boldsymbol{\mu} - R_f \iota)}{\iota'\Sigma^{-1}(\boldsymbol{\mu} - R_f \iota)} = \frac{\Sigma^{-1}\tilde{\boldsymbol{\mu}}}{\iota'\Sigma^{-1}\tilde{\boldsymbol{\mu}}}$

- $\tilde{\boldsymbol{\mu}} = \boldsymbol{\mu} - R_f \iota$: Vector of expected excess returns over the risk-free rate.
- The denominator ensures weights sum to 1 for the risky asset portfolio.

**Optimal Portfolio for an Investor with Risk Aversion $\gamma$ (Mean-Variance Utility):**
The investor allocates a portion of their wealth to the Tangency Portfolio and the rest to the risk-free asset. The weight in the Tangency Portfolio is:

$w_{risky}^* = \frac{E[R_{TAN}] - R_f}{\gamma \sigma_{TAN}^2}$

The weights in individual risky assets are then $w_i^* = w_{risky}^* \times w_{TAN,i}$.

Alternatively, the direct weights in risky assets for an investor maximizing $E[R_p] - \frac{\gamma}{2} \sigma_p^2$ with a risk-free asset are:

$\mathbf{w}^* = \frac{1}{\gamma} \Sigma^{-1} \tilde{\boldsymbol{\mu}}$

Note: These weights do not sum to 1 if $\gamma$ and inputs are such that leverage or risk-free lending is optimal. The remainder $(1 - \iota'\mathbf{w}^*)$ is in the risk-free asset.

### 4. Certainty Equivalent (CE)

For an investor with quadratic utility or when returns are normally distributed, maximizing expected utility is equivalent to maximizing the certainty equivalent.

**[[Content/Key Concepts/Certainty Equivalent|Certainty Equivalent]] (Quadratic Utility Approximation):**

$CE(\mathbf{w}) = \mathbf{w}'\boldsymbol{\mu} - \frac{\gamma}{2} \mathbf{w}'\Sigma\mathbf{w}$

- $\gamma$: Coefficient of absolute risk aversion (for quadratic utility) or relative risk aversion (if utility is power utility and this is an approximation for portfolio returns).

**Optimal Weights Maximizing CE (No Risk-Free Asset, Full Investment):**

$\mathbf{w}^* = \mathbf{w}_{GMV} + \frac{1}{\gamma} \left(\Sigma^{-1} - \frac{\Sigma^{-1}\iota\iota'\Sigma^{-1}}{\iota'\Sigma^{-1}\iota}\right) \boldsymbol{\mu} = \mathbf{w}_{GMV} + \frac{1}{\gamma} A_\Sigma \boldsymbol{\mu}$

Where $A_\Sigma = \Sigma^{-1} - \mathbf{w}_{GMV}\iota'\Sigma^{-1}$ is a specific matrix form.

## II. Parameter Estimation and Uncertainty

This section deals with the challenges of estimating the inputs ($\boldsymbol{\mu}$, $\Sigma$) and the consequences of estimation error.

### 1. Sample Moments (Plug-in Estimates)

**Sample Mean ($\hat{\boldsymbol{\mu}}$):**

$\hat{\boldsymbol{\mu}} = \frac{1}{T} \sum_{t=1}^{T} \mathbf{R}_t$

- $\mathbf{R}_t$: Vector of asset returns at time $t$
- $T$: Number of time series observations

Distribution (if returns are i.i.d. Normal): $\hat{\boldsymbol{\mu}} \sim N(\boldsymbol{\mu}, \Sigma/T)$

**Sample Covariance Matrix ($\hat{\Sigma}$):**

$\hat{\Sigma} = \frac{1}{T-1} \sum_{t=1}^{T} (\mathbf{R}_t - \hat{\boldsymbol{\mu}})(\mathbf{R}_t - \hat{\boldsymbol{\mu}})'$

Distribution (if returns are i.i.d. Normal, scaled by $T/(T-1)$): $T\hat{\Sigma} \sim W_N(T-1, \Sigma)$ (Wishart distribution)

**Expected Value of Inverse Sample Covariance Matrix (scaled):**
If $X \sim W_N(\nu, \Phi)$, then $E[X^{-1}] = \Phi^{-1}/(\nu-N-1)$.

For $\hat{\Sigma} \sim W_N(T-1, \Sigma)/T$, then $E[\hat{\Sigma}^{-1}] = \frac{T-N-2}{T} \Sigma^{-1}$ (for $T-N-2 > 0$). This shows the sample inverse covariance is a biased estimator of $\Sigma^{-1}$.

**Expected Squared Sharpe Ratio of Sample Tangency Portfolio:**

$E[\hat{\boldsymbol{\mu}}'\Sigma^{-1}\hat{\boldsymbol{\mu}}] = (\boldsymbol{\mu}'\Sigma^{-1}\boldsymbol{\mu} + N/T)$

(Note: This is for $\Sigma$ known, $\hat{\boldsymbol{\mu}}$ random. The formula sheet uses $(T\boldsymbol{\mu}'\Sigma^{-1}\boldsymbol{\mu} + N)/T$ which implies a scaling difference in definition of $\hat{\boldsymbol{\mu}}'\Sigma^{-1}\hat{\boldsymbol{\mu}}$ or the context).

### 2. [[Content/Key Concepts/Kan-Zhao Framework|Kan-Zhao Framework]] for Economic Losses from Parameter Uncertainty

This framework quantifies the expected utility loss from using estimated parameters.

**Loss Function:** $L(\mathbf{w}, \hat{\mathbf{w}}) = U(\mathbf{w}) - U(\hat{\mathbf{w}})$

$U(\mathbf{w}) = \frac{1}{2\gamma} \boldsymbol{\mu}'\Sigma^{-1}\boldsymbol{\mu}$ (Max utility for tangency portfolio with risk-free asset)

**Case 1: Uncertain $\boldsymbol{\mu}$, Known $\Sigma$**
Expected Loss: $E[L(\mathbf{w}^*, \hat{\mathbf{w}})|\Sigma] = \frac{N}{2\gamma T}$

**Case 2: Known $\boldsymbol{\mu}$, Uncertain $\Sigma$**
Expected Loss: $E[L(\mathbf{w}^*, \hat{\mathbf{w}})|\boldsymbol{\mu}] = \frac{1}{2\gamma} \boldsymbol{\mu}'\Sigma^{-1}\boldsymbol{\mu}(1-k)$

$k = \frac{T-N-2}{T} \left(2-\frac{(T-N-1)(T-N-4)}{T(T-2)}\right)$

**Case 3: Both $\boldsymbol{\mu}$ and $\Sigma$ Uncertain**
Expected Loss: $E[L(\mathbf{w}^*, \hat{\mathbf{w}})] = \frac{1}{2\gamma} \boldsymbol{\mu}'\Sigma^{-1}\boldsymbol{\mu}(1-k) + \frac{N}{2\gamma} \frac{(T-N-1)(T-N-2)(T-N-4)}{T(T-2)}$

**Optimal Scaling Factor $c^{**}$ for $\hat{\mathbf{w}} = \frac{c}{\gamma} \hat{\Sigma}^{-1}\hat{\boldsymbol{\mu}}$:**

$c^{**} = \frac{\boldsymbol{\mu}'\Sigma^{-1}\boldsymbol{\mu} + N/T}{\boldsymbol{\mu}'\Sigma^{-1}\boldsymbol{\mu}} \left(\frac{T(T-2)}{(T-N-1)(T-N-4)}\right)$

This $c^{**}$ depends on true (unknown) parameters. A simplified version $c_1 = \frac{T(T-2)}{(T-N-1)(T-N-4)}$ is used when $\boldsymbol{\mu}'\Sigma^{-1}\boldsymbol{\mu} \to \infty$.

**Three-Fund Portfolio Weights (Kan & Zhou combination):**

$\hat{\mathbf{w}}^{(c,d)} = \frac{1}{\gamma}(c\hat{\Sigma}^{-1}\hat{\boldsymbol{\mu}} + d\hat{\Sigma}^{-1}\iota)$

Optimal $c^{**}$ and $d^{**}$ are complex functions of $N$, $T$, and true parameters (see Lecture 5 slides).

### 3. [[Content/Key Concepts/Shrinkage Estimation|Shrinkage Estimation]]

**General Form:**

$\hat{\boldsymbol{\theta}}_{shrink} = \alpha \cdot \text{Target} + (1-\alpha) \cdot \text{Sample Estimate}$

- $\alpha$: Shrinkage intensity, $0 \leq \alpha \leq 1$.

**[[Content/Key Concepts/James-Stein Estimator|James-Stein Estimator]] for Means (target $\mu_0 \iota$):**

$\hat{\boldsymbol{\mu}}_{JS} = \mu_0 \iota + (1-\delta)(\hat{\boldsymbol{\mu}} - \mu_0 \iota)$

Optimal $\delta^* = \min\left[1, \frac{(N-2)/T}{(\hat{\boldsymbol{\mu}} - \mu_0 \iota)'\Sigma^{-1}(\hat{\boldsymbol{\mu}} - \mu_0 \iota)}\right]$ (assuming $\Sigma$ known or well-estimated).

If $\mu_0$ is the grand mean $\bar{\mu}$, $\hat{\boldsymbol{\mu}}_{JS} = \bar{\mu}\iota + (1-\delta)(\hat{\boldsymbol{\mu}} - \bar{\mu}\iota)$.

**[[Content/Key Concepts/Ledoit-Wolf Shrinkage|Ledoit-Wolf (2003)]] Covariance Shrinkage (target $\hat{\Sigma}^*$ from a factor model):**

$\hat{\Sigma}_{LW} = \delta\hat{\Sigma}^* + (1-\delta)\hat{\Sigma}^S$ (where $\hat{\Sigma}^S$ is sample covariance)

Optimal $\delta \approx \frac{1}{T} \frac{C}{A-B}$ (see lecture 6 for definitions of $A$, $B$, $C$ related to asy. variances and biases).

**[[Content/Key Concepts/Eigenvalue Shrinkage|Ledoit-Wolf (2004)]] Eigenvalue Shrinkage (target: identity matrix scaled by average variance, or constant correlation matrix):**

$\hat{\Sigma} = V\tilde{D}V'$ where $\tilde{D}$ contains shrunk eigenvalues.

Linear shrinkage of eigenvalues: $\tilde{d}_i = (1-\alpha)d_i + \alpha \cdot target$.

### 4. [[Content/Key Concepts/Portfolio Constraints|Portfolio Constraints]] & [[Content/Key Concepts/Shrinkage Interpretation of Constraints|Shrinkage Interpretation]] (Jagannathan & Ma, 2003)

Imposing constraints (e.g., no short-selling $w_i \geq 0$) on GMV portfolio is equivalent to solving an unconstrained GMV problem with a modified covariance matrix $\tilde{S}$:

$\tilde{S} = S + (\boldsymbol{\delta}\iota' + \iota\boldsymbol{\delta}') - (\boldsymbol{\lambda}\iota' + \iota\boldsymbol{\lambda}')$

- $\boldsymbol{\delta}$, $\boldsymbol{\lambda}$: Vectors of KKT multipliers for upper and lower bound constraints, respectively.

Binding constraints effectively shrink certain covariance elements, reducing impact of estimation error.

## III. Regression-Based Portfolio Choice & Performance Testing

### 1. [[Content/Key Concepts/Inverse Covariance Matrix|Inverse Covariance Matrix from Regressions]] (Stevens, 1998)

For $N$ assets, regress each $r_i$ on all other $r_j$ ($j \neq i$): $r_i = a_i + \sum_{j \neq i} \phi_{ij} r_j + \epsilon_i$.

The elements of $\Sigma^{-1} = (\theta_{ij})$ are:

$\theta_{ii} = 1/V(\epsilon_i)$
$\theta_{ij} = -\phi_{ij}/V(\epsilon_i)$

(Note: from regression of $i$ on $j$, for $\theta_{ij}$ use $\phi_{ij}$ from $i$'s regression; for $\theta_{ji}$ use $\phi_{ji}$ from $j$'s regression. Symmetry needs to be handled).

### 2. [[Content/Key Concepts/Britten-Jones Regression|Britten-Jones (1999) Regression]] for Tangency Portfolio

Regress a vector of ones $\iota$ on the matrix of excess returns $R$: $\iota = Rb + u$.

The OLS estimate $\hat{b}$ gives tangency portfolio weights: $\mathbf{w}_{TAN} = \frac{\hat{b}}{\iota'\hat{b}}$.

Allows for standard OLS inference on weights.

### 3. [[Content/Key Concepts/Kempf-Memmel Regression|Kempf-Memmel (2006) Regression]] for GMV Portfolio

Regress $r_{t1}$ on $(r_{t1} - r_{tj})$ for $j = 2, ..., N$: $r_{t1} = \mu_p + \sum_{j=2}^{N} w_j (r_{t1} - r_{tj}) + \epsilon_t$.

The estimated coefficients $\hat{w}_j$ are GMV weights for assets $j = 2, ..., N$.
$\hat{w}_1 = 1 - \sum_{j=2}^{N} \hat{w}_j$.

### 4. [[Content/Key Concepts/Sharpe Ratio Testing|Sharpe Ratio Testing]] (Ledoit & Wolf, 2008)

To test $H_0: SR_i = SR_j$ (or $\Delta = SR_i - SR_j = 0$).

Test statistic: $\hat{\Delta} = \hat{SR}_i - \hat{SR}_j$.

Asymptotic distribution: $\sqrt{T}(\hat{\Delta} - \Delta) \stackrel{d}{\to} N(0, \nabla'f(v) \Psi \nabla f(v))$.

$v = (\mu_i, \mu_j, E[r_i^2], E[r_j^2])'$
$\Delta = f(a,b,c,d) = \frac{a}{\sqrt{c-a^2}} - \frac{b}{\sqrt{d-b^2}}$

$\nabla f(v)$ is the gradient of $f$.
$\Psi$ is the HAC covariance matrix of $\sqrt{T}(\hat{v} - v)$.

Standard error: $s(\hat{\Delta}) = \sqrt{\frac{\nabla'f(\hat{v}) \hat{\Psi} \nabla f(\hat{v})}{T}}$.

Use HAC estimators (Newey-West, Andrews) or studentized time-series bootstrap for $\hat{\Psi}$ and inference.

### 5. Variance Testing (Ledoit & Wolf, 2011)

To test $H_0: \sigma_i^2 = \sigma_j^2$ (or $\Delta = \log \sigma_i^2 - \log \sigma_j^2 = 0$).

Similar approach using $\Delta = f(a,b,c,d) = \log(c-a^2) - \log(d-b^2)$.

## IV. Large-N Portfolio Allocation

### 1. [[Content/Key Concepts/Factor Models for Large-N Covariance Matrices|Factor Model for Covariance Matrix]] $\Sigma = B\Sigma_f B' + \Sigma_\epsilon$

Reduces dimensionality. $\Sigma_\epsilon$ often assumed diagonal.

Inverse (Woodbury identity): $\Sigma^{-1} = \Sigma_\epsilon^{-1} - \Sigma_\epsilon^{-1} B (B' \Sigma_\epsilon^{-1} B + \Sigma_f^{-1})^{-1} B' \Sigma_\epsilon^{-1}$.

### 2. [[Content/Key Concepts/POET|POET (Principal Orthogonal complEment Thresholding)]]

Estimate $K$ factors using PCA on sample covariance $\hat{S}$. $\hat{S} = \sum_{i=1}^{K} \hat{d}_i \hat{\lambda}_i \hat{\lambda}_i' + \hat{R}_\epsilon$ (factor part + residual part).

Estimate residual covariance matrix $\hat{\Sigma}_\epsilon$ from residuals of factor model.

Apply thresholding to $\hat{\Sigma}_\epsilon$ to enforce sparsity (e.g., hard thresholding: set elements below $\tau$ to 0). Let this be $\tilde{\Sigma}_\epsilon^{(sparse)}$.

$\hat{\Sigma}_{POET} = \sum_{i=1}^{K} \hat{d}_i \hat{\lambda}_i \hat{\lambda}_i' + \tilde{\Sigma}_\epsilon^{(sparse)}$.

### 3. [[Content/Key Concepts/Nodewise Regression|Nodewise Regression]] for $\Sigma^{-1}$

For each asset $i$, run Lasso regression: $r_i = a_i + \sum_{j \neq i} \phi_{ij} r_j + \epsilon_i$.

Construct $\hat{\Sigma}^{-1}$ using $\hat{\theta}_{ii} = 1/\hat{V}(\epsilon_i)$ and $\hat{\theta}_{ij} = -\hat{\phi}_{ij}/\hat{V}(\epsilon_i)$.

### 4. [[Content/Key Concepts/Graphical Lasso|Graphical Lasso]] for $\Sigma^{-1}$

Estimate $\Theta = \Sigma^{-1}$ by maximizing penalized log-likelihood:

$\max_{\Theta} (\log\det(\Theta) - \text{tr}(S\Theta) - \lambda ||\Theta||_{1,\text{off}})$

$||\Theta||_{1,\text{off}}$: L1 norm of off-diagonal elements. Promotes sparsity.

## V. Long-Term Portfolio Allocation

### 1. [[Content/Key Concepts/Power Utility|Power Utility]] and Lognormal Returns

**Power Utility:** $U(W) = \frac{W^{1-\gamma}}{1-\gamma}$ (RRA = $\gamma$)

**Log return of portfolio (Campbell-Viceira approximation):**

$r_{p,t+1} - r_{f,t+1} \approx \alpha_t'(r_{t+1} - r_{f,t+1}\iota) + \frac{1}{2}\alpha_t'\sigma_t^2 - \frac{1}{2}\alpha_t'\Sigma_t\alpha_t$

Lowercase $r$ are log returns. $\sigma_t^2$ is vector of individual asset log return variances.

**Myopic optimal allocation (single risky asset, log returns):**

$\alpha_t = \frac{1}{\gamma\sigma^2}(E_t[r_{t+1}] - r_{f,t+1} + \frac{1}{2}\sigma^2)$

### 2. [[Content/Key Concepts/Dynamic Portfolio Choice|Dynamic Portfolio Choice]] & [[Content/Key Concepts/Bellman Equation|Bellman Equation]]

**Value Function:** $V(\tau, W_t, z_t)$ = max expected utility with $\tau$ periods left, current wealth $W_t$, state vector $z_t$.

**Bellman Equation:** $V(\tau, W_t, z_t) = \max_{x_t} E_t[V(\tau-1, W_{t+1}, z_{t+1})]$

$W_{t+1} = W_t(x_t'r_{t+1} + R_{f,t})$ (using simple returns here for wealth accumulation)

For Power Utility: $V(\tau, W_t, z_t) = \frac{W_t^{1-\gamma}}{1-\gamma} \psi(\tau, z_t)$.

Bellman simplifies to an equation for $\psi(\tau, z_t)$.

### 3. [[Content/Key Concepts/Hedging Demand|Hedging Demand]]

**Optimal dynamic portfolio** $x_t^* = \text{Myopic Demand} + \text{Hedging Demand}$.

**Continuous Time Solution (Merton):**

$x_t^* = \frac{1}{\gamma}(\Sigma_t^p)^{-1}\mu_t^p - (\Sigma_t^p)^{-1}D_t^p \rho_t (D_t^z)' \frac{W_t V_{Wz}}{V_{WW}}$

(Slight variation from formula sheet, $\frac{V_{Wz}}{W_t V_{WW}}$ is related to $\frac{\eta}{\gamma}$)

- $\mu_t^p$: Conditional expected excess returns.
- $D_t^p$, $D_t^z$: Diffusion matrices for asset prices and state variables.
- $\rho_t$: Correlation matrix between asset and state variable innovations.
- $V_{Wz}$: Cross-partial derivative of value function w.r.t wealth and state. $V_{WW}$: Second derivative w.r.t. wealth.

The term $-\frac{W_t V_{Wz}}{V_{WW}}$ can be written as $\frac{\gamma}{\eta}$ where $\eta = \frac{V_W}{V_{Wz}}$ (investor's aversion to state variable $z$).

### 4. [[Content/Key Concepts/Parameter Uncertainty in Long-Term Allocation|Parameter Uncertainty in Long-Term Allocation]] ([[Content/Key Concepts/Barberis Framework|Barberis, 2000]])

Parameter uncertainty (especially about predictability) makes stocks appear riskier over longer horizons.

Often leads to more conservative equity allocations than if uncertainty were ignored.

Reduces sensitivity to predictive variables.

### 5. [[Content/Key Concepts/Parametric Portfolio Policies|Parametric Portfolio Policies]]

**Large-N (Brandt, Santa-Clara, Valkanov, 2009):**

$w_{i,t} = w_{0,i,t} + \frac{1}{N_t} \theta'y_{i,t}$

$y_{i,t}$: Asset characteristics. $\theta$ estimated by maximizing average sample utility.

**Conditional/Timing (Brandt & Santa-Clara, 2006 - [[Content/Key Concepts/Augmented Asset Space|Augmented Asset Space]]):**

$w_t = \Theta z_t$. Solved by unconditional mean-variance on "managed portfolios" with returns $z_t \otimes r_{t+1}$.

## VI. Key Statistical Relationships (from Kan & Zhao, 2007 context)

These are useful for derivations involving sample moments under normality.

Let excess returns $r_t \sim \text{i.i.d.} N(\mu, \Sigma)$.
Sample mean $\hat{\mu} = \frac{1}{T}\sum r_t$. Sample covariance $\hat{\Sigma} = \frac{1}{T-1}\sum (r_t - \hat{\mu})(r_t - \hat{\mu})'$.

$E[\hat{\mu}] = \mu$
$Cov(\hat{\mu}) = \Sigma/T$
$(T-1)\hat{\Sigma} \sim W_N(T-1, \Sigma)$ (Wishart distribution with $T-1$ degrees of freedom)
$E[\hat{\Sigma}] = \Sigma$ (if $T-1$ in denominator)
$E[\hat{\Sigma}^{-1}] = \frac{T-N-2}{T-1}\Sigma^{-1}$ (using $T-1$ in $\hat{\Sigma}$ definition, for $T-N-2 > 0$)

The formula sheet uses $\hat{\Sigma} \sim W_N(T-1, \Sigma)/T$, implying $E[\hat{\Sigma}^{-1}] = \frac{T-N-2}{T}\Sigma^{-1}$. Be consistent with definitions.

$\sqrt{T}(\hat{\mu} - \mu) \stackrel{d}{\to} N(0, \Sigma)$
$E[\hat{\mu}'\Sigma^{-1}\hat{\mu}] = \mu'\Sigma^{-1}\mu + \frac{N}{T}$ (This is for $\Sigma$ known).

The formula sheet: $E[\hat{\mu}'\Sigma^{-1}\hat{\mu}] = (T\mu'\Sigma^{-1}\mu + N)/T$. This is equivalent.

For $\tilde{\Sigma} = \Sigma^{-1/2}\hat{\Sigma}\Sigma^{-1/2}$, if $T\hat{\Sigma} \sim W_N(T-1, \Sigma)$, then $T\tilde{\Sigma} \sim W_N(T-1, I_N)$.

$E[(T\tilde{\Sigma})^{-1}] = I_N/(T-1-N-1) = I_N/(T-N-2)$. So $E[\tilde{\Sigma}^{-1}] = \frac{T-N-2}{T}I_N$.

$E[(\Sigma^{-1/2}\hat{\Sigma}\Sigma^{-1/2})^{-2}] = \frac{(T-N-1)(T-N-2)(T-N-4)}{T^2(T-2)}I_N$ (from formula sheet, implies $\hat{\Sigma}$ is scaled by $1/T$ in its Wishart definition $W_N(T-1, \Sigma)/T$).

This detailed sheet should help you connect the various formulas to their underlying concepts and applications within the course. Remember to pay attention to the specific definitions and assumptions used when applying these formulas.