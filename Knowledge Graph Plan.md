# Portfolio Management Knowledge Graph Plan

**Goal:** To create a deeply interconnected knowledge graph in your Obsidian vault that helps you understand and revise the "Portfolio Management" course material.

**Phase 1: Setting up the Foundational Structure (AI will do this)**

The AI will create the following files and initial structure:

1.  **Main Hub Note:**
    *   `Hubs/Portfolio Management Hub.md`: Central dashboard.

2.  **Category Hub Notes (within a `Hubs` folder):**
    *   `Hubs/Lectures Hub.md`
    *   `Hubs/Weekly Materials Hub.md`
    *   `Hubs/Key Concepts Hub.md`
    *   `Hubs/Reference Materials Hub.md`
    *   `Hubs/Exams Hub.md`

3.  **Placeholder Content Notes (organized into folders):**

    *   **Lectures (`Content/Lectures/`):**
        *   `Content/Lectures/Lecture 01 (FEM21010_Lecture_1).md`
        *   `Content/Lectures/Lecture 02 (FEM21010_Lecture_2).md`
        *   ... (for all 12 lectures)
        *   `Content/Lectures/Lecture 12 (FEM21010_Lecture_12).md`

    *   **Weekly Materials (`Content/Weekly Materials/`):**
        *   `Content/Weekly Materials/Week 1 Exercises (Exercises_Week_1).md`
        *   `Content/Weekly Materials/Week 1 Answers (Answers_Week_1).md`
        *   ... (for all 7 weeks)
        *   `Content/Weekly Materials/Week 7 Exercises (Exercises_Week_7).md`
        *   `Content/Weekly Materials/Week 7 Answers (Answers_Week_7).md`

    *   **Reference Materials (`Content/Reference Materials/`):**
        *   `Content/Reference Materials/Course Guide (FEM21010_guide_2425).md`
        *   `Content/Reference Materials/Formula Sheet (FEM21010_Sheet).md`

    *   **Exams (`Content/Exams/`):**
        *   `Content/Exams/Exam (FEM21010_Exam1920a).md`

    *   **Key Concepts (`Content/Key Concepts/`):** This folder will be populated as concepts are identified. Examples:
        *   `Content/Key Concepts/Modern Portfolio Theory.md`
        *   `Content/Key Concepts/CAPM.md`

**Phase 2: Populating and Connecting (Your Role, with AI Assistance)**

1.  **Populate Notes:**
    *   Open each PDF from `Inputdocs`.
    *   Copy relevant text.
    *   Paste into the corresponding Markdown note.

2.  **Identify Key Concepts:**
    *   Identify key terms, theories, formulas.
    *   Ensure a dedicated note exists in `Content/Key Concepts/` for each.

3.  **Create Links (The Knowledge Graph):**
    *   **Within Content Notes:** Link key concepts to their dedicated notes (e.g., `[[Content/Key Concepts/CAPM]]`).
    *   **Within Key Concept Notes:** Define the concept, add formulas, link to related concepts.
    *   **Link to Hubs:** Link individual content notes from their respective Hub notes. Populate `Hubs/Key Concepts Hub.md` with links to all concept notes.
    *   **Interlink Weekly Materials:** Link exercises to answers.

**Phase 3: Iteration and Refinement (Ongoing)**

*   Continuously update and refine the graph as you study.
*   Use Obsidian's graph view to visualize connections.
*   Integrate existing notes (`Course Summary.md`, `Key Formulas.md`, etc.) into the structure.

**Example Workflow Snippet:**

1.  Populating `Content/Lectures/Lecture 03 (FEM21010_Lecture_3).md`.
2.  Encounter "Efficient Frontier."
3.  Create/ensure `Content/Key Concepts/Efficient Frontier.md` exists.
4.  In the lecture note, write: `[[Content/Key Concepts/Efficient Frontier]]`.
5.  In `Content/Key Concepts/Efficient Frontier.md`, define it and link to related concepts like `[[Content/Key Concepts/Modern Portfolio Theory]]`.
6.  Ensure `Hubs/Key Concepts Hub.md` links to `[[Content/Key Concepts/Efficient Frontier]]`.
